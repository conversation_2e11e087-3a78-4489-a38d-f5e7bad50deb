import 'package:dio/dio.dart';
import 'CategoryModel.dart'; // import your model here

Future<List<Category>> fetchCategory() async {
  try {
    final dio = Dio();
    final response = await dio.get('https://project-yhx7.onrender.com/api/v1/categories');

    if (response.statusCode == 200) {
      print(response.data);
      final List<dynamic> productsJson = response.data['data'];
      return productsJson.map((e) => Category.fromJson(e)).toList();
    } else {
      throw Exception('Failed to load products');
    }
  } catch (e) {
    print('Error: $e');
    return [];
  }
}
