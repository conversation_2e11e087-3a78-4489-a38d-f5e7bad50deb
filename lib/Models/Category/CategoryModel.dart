class Category {
  final String id;
  final String name;
  final String slug;

  final String createdAt;
  final String updatedAt;

  Category({
    required this.id,
    required this.name,
    required this.slug,

    required this.createdAt,
    required this.updatedAt,

  });

  factory Category.fromJson(Map<String, dynamic> json) {
    return Category(
      id: json["_id"]?.toString() ?? '',                      // fallback empty string if null
      name: json["name"]?.toString() ?? 'No Title',
      slug: json["slug"]?.toString() ?? '',

      createdAt: json["createdAt"]?.toString() ?? '',
      updatedAt: json["updatedAt"]?.toString() ?? '',

    );
  }
}
