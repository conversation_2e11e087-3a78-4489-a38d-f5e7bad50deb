class OrderResponse {
  final String status;
  final int results;
  final OrderData? data;

  OrderResponse({
    required this.status,
    required this.results,
    this.data,
  });

  factory OrderResponse.fromJson(Map<String, dynamic> json) {
    return OrderResponse(
      status: json['status'] ?? '',
      results: json['results'] ?? 0,
      data: json['data'] != null ? OrderData.fromJson(json['data']) : null,
    );
  }
}

class OrderData {
  final String? id;
  final String? user;
  final List<OrderProduct>? products;
  final double? totalPrice;
  final double? totalPriceAfterDiscount;
  final String? createdAt;
  final String? updatedAt;

  OrderData({
    this.id,
    this.user,
    this.products,
    this.totalPrice,
    this.totalPriceAfterDiscount,
    this.createdAt,
    this.updatedAt,
  });

  factory OrderData.fromJson(Map<String, dynamic> json) {
    return OrderData(
      id: json['_id'],
      user: json['user'],
      products: (json['products'] as List<dynamic>?)
          ?.map((e) => OrderProduct.fromJson(e))
          .toList(),
      totalPrice: (json['totalPrice'] as num?)?.toDouble(),
      totalPriceAfterDiscount: (json['totalPriceAfterDiscount'] as num?)?.toDouble(),
      createdAt: json['createdAt'],
      updatedAt: json['updatedAt'],
    );
  }
}

class OrderProduct {
  final Product? product;
  final String? size;
  final int? quantity;
  final int? price;

  OrderProduct({
    this.product,
    this.size,
    this.quantity,
    this.price,
  });

  factory OrderProduct.fromJson(Map<String, dynamic> json) {
    return OrderProduct(
      product: json['product'] != null ? Product.fromJson(json['product']) : null,
      size: json['size'],
      quantity: json['quantity'],
      price: json['price'],
    );
  }
}

class Product {
  final String? id;
  final String? name;
  final int? price;
  final String? imageCover;
  final List<String>? sizes;

  Product({
    this.id,
    this.name,
    this.price,
    this.imageCover,
    this.sizes,
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['_id'],
      name: json['name'],
      price: json['price'],
      imageCover: json['imageCover'],
      sizes: (json['sizes'] as List<dynamic>?)?.map((e) => e.toString()).toList(),
    );
  }
}
