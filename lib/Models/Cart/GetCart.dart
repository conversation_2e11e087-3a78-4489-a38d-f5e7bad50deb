import 'package:dio/dio.dart';
import 'package:get_storage/get_storage.dart';

import 'Model.dart';

final Dio _dio = Dio();
final box =GetStorage();
Future<OrderResponse> fetchOrder() async {
  try {
    final response = await _dio.get('https://project-yhx7.onrender.com/api/v1/cart',options: Options(
      headers: {
        'Authorization': 'Bearer ${box.read('token')}',
        'Content-Type': 'application/json', // optional, depends on API
      },
    ),);

    if (response.statusCode == 200) {
      return OrderResponse.fromJson(response.data);
    } else {
      throw Exception('Failed to load order: ${response.statusCode}');
    }
  } catch (e) {
    print('Error fetching order: $e');
    rethrow;
  }
}
