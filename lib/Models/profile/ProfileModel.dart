class UserModel {
  final String? id;
  final String? name;
  final String? email;
  final bool? isResetCodeVerified;
  final String? role;
  final String? image;
  final bool? active;
  final List<String>? wishlist;
  final bool? isPhoneOtpVerified;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final Coordinates? coordinates;

  UserModel({
    this.id,
    this.name,
    this.email,
    this.isResetCodeVerified,
    this.role,
    this.image,
    this.active,
    this.wishlist,
    this.isPhoneOtpVerified,
    this.createdAt,
    this.updatedAt,
    this.coordinates,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['_id'] ?? json['id'],
      name: json['name'],
      email: json['email'],
      isResetCodeVerified: json['isResetCodeVerified'],
      role: json['role'],
      image: json['image'],
      active: json['active'],
      wishlist: List<String>.from(json['wishlist'] ?? []),
      isPhoneOtpVerified: json['isPhoneOtpVerified'],
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      coordinates: json['address']?['coordinates'] != null
          ? Coordinates.fromJson(json['address']['coordinates'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "_id": id,
      "name": name,
      "email": email,
      "isResetCodeVerified": isResetCodeVerified,
      "role": role,
      "image": image,
      "active": active,
      "wishlist": wishlist,
      "isPhoneOtpVerified": isPhoneOtpVerified,
      "createdAt": createdAt?.toIso8601String(),
      "updatedAt": updatedAt?.toIso8601String(),
      "address": {
        "coordinates": coordinates?.toJson(),
      },
    };
  }
}

class Coordinates {
  final String? type;
  final List<dynamic>? coordinates;

  Coordinates({this.type, this.coordinates});

  factory Coordinates.fromJson(Map<String, dynamic> json) {
    return Coordinates(
      type: json['type'],
      coordinates: json['coordinates'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "type": type,
      "coordinates": coordinates,
    };
  }
}
