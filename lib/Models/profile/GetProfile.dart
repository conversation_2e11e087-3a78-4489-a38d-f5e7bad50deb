import 'package:dio/dio.dart';
import 'package:get_storage/get_storage.dart';

import 'ProfileModel.dart';

Future<UserModel?> fetchUser() async {
  final dio = Dio();
  final box=GetStorage();
  try {
    final response = await dio.get('https://project-yhx7.onrender.com/api/v1/users/me',options: Options(
      headers: {
        'Authorization': 'Bearer ${box.read('token')}',
        'Content-Type': 'application/json', // optional, depends on API
      },
    ),);
    if (response.statusCode == 200 && response.data['status'] == 'success') {
      return UserModel.fromJson(response.data['data']);
    }
  } catch (e) {
    print("Error: $e");
  }
  return null;
}
