class Product {
  final String id;
  final String title;
  final String description;
  final double price;
  final String imageCover;
  final List<String> images;
  final List<String> sizes; // ✅ new field
  final int quantity;
  final int sold;
  final double? priceAfterDiscount;
  final double ratingsAverage;
  final int ratingsQuantity;
  final String createdAt;
  final String updatedAt;
  final String? categoryName;
  final String? brandName;
   final bool? isWishlisted;

  Product({
    required this.id,
    required this.title,
    required this.description,
    required this.price,
    required this.imageCover,
    required this.images,
    required this.sizes, // ✅ constructor updated
    required this.quantity,
    required this.sold,
    required this.isWishlisted,
    this.priceAfterDiscount,
    required this.ratingsAverage,
    required this.ratingsQuantity,
    required this.createdAt,
    required this.updatedAt,
    this.categoryName,
    this.brandName,

  });

  factory Product.fromJson(Map<String, dynamic> json) {
    // ✅ default image
    const defaultImage =
        'https://res.cloudinary.com/dvow5shsk/image/upload/v1747647459/products/covers/pdn6fkifzxs2yfmxnp0f.jpg';

    // ✅ handle images
    List<String> images = (json["images"] as List<dynamic>?)
        ?.map((e) => e.toString())
        .toList() ??
        [];

    // ✅ ensure at least 3 images
    while (images.length < 3) {
      images.add(defaultImage);
    }

    // ✅ handle sizes
    List<String> sizes = (json["sizes"] as List<dynamic>?)
        ?.map((e) => e.toString())
        .toList() ??
        [];

    return Product(
      id: json["_id"]?.toString() ?? '',
      title: json["name"]?.toString() ?? 'No Title',
      description: json["description"]?.toString() ?? '',
      price: (json["price"] != null) ? (json["price"] as num).toDouble() : 0.0,
      imageCover: json["imageCover"]?.toString() ?? defaultImage,
      images: images,
      sizes: sizes,
      quantity: (json["quantity"] is int)
          ? json["quantity"]
          : int.tryParse(json["quantity"]?.toString() ?? '') ?? 0,
      sold: (json["sold"] is int)
          ? json["sold"]
          : int.tryParse(json["sold"]?.toString() ?? '') ?? 0,
      priceAfterDiscount: json["priceAfterDiscount"] != null
          ? (json["priceAfterDiscount"] as num).toDouble()
          : null,
      ratingsAverage: (json["ratingsAverage"] != null)
          ? (json["ratingsAverage"] as num).toDouble()
          : 0.0,
      ratingsQuantity: (json["ratingsQuantity"] is int)
          ? json["ratingsQuantity"]
          : int.tryParse(json["ratingsQuantity"]?.toString() ?? '') ?? 0,
      createdAt: json["createdAt"]?.toString() ?? '',
      updatedAt: json["updatedAt"]?.toString() ?? '',
      categoryName: json["category"]?.toString(),
      brandName: json["brand"]?["name"]?.toString(),
      isWishlisted: (json["isWishlisted"] as bool)
    );
  }
}
