import 'package:dio/dio.dart';
import 'package:get_storage/get_storage.dart';
import 'ProductModel.dart'; // import your model here
//relevant
final box=GetStorage();
Future<List<Product>> fetchProducts() async {
  try {
    final dio = Dio();

    final response = await dio.get('https://project-yhx7.onrender.com/api/v1/products/wishlist-compare',
      options: Options(
    headers: {
    'Authorization': 'Bearer ${box.read('token')}',
    'Content-Type': 'application/json', // optional, depends on API
    },
    ),);

    if (response.statusCode == 200) {
      final List<dynamic> productsJson = response.data['data'];
      final products = productsJson.map((e) => Product.fromJson(e)).toList();

      // Sort products by ratingsAverage, then ratingsQuantity, then sold
      products.sort((a, b) {
        // Compare ratingsAverage
        final ratingAvgComp = b.ratingsAverage.compareTo(a.ratingsAverage);
        if (ratingAvgComp != 0) return ratingAvgComp;

        // If ratingsAverage is equal, compare ratingsQuantity
        final ratingQtyComp = b.ratingsQuantity.compareTo(a.ratingsQuantity);
        if (ratingQtyComp != 0) return ratingQtyComp;

        // If both are equal, compare sold
        return b.sold.compareTo(a.sold);
      });

      return products;
    } else {
      throw Exception('Failed to load products');
    }
  } catch (e) {
    print('Error: $e');
    return [];
  }
}
Future<List<Product>> fetchProductsNormal() async {
  try {
    final dio = Dio();
    final response = await dio.get('https://project-yhx7.onrender.com/api/v1/products/wishlist-compare',options: Options(
      headers: {
        'Authorization': 'Bearer ${box.read('token')}',
        'Content-Type': 'application/json', // optional, depends on API
      },
    ),);

    if (response.statusCode == 200) {
      print(response.data);
      final List<dynamic> productsJson = response.data['data'];
      return productsJson.map((e) => Product.fromJson(e)).toList();
    } else {
      throw Exception('Failed to load products');
    }
  } catch (e) {
    print('Error: $e');
    return [];
  }
}
Future<List<Product>> fetchProductsHigh() async {
  try {
    final dio = Dio();
    final response = await dio.get('https://project-yhx7.onrender.com/api/v1/products/wishlist-compare',options: Options(
      headers: {
        'Authorization': 'Bearer ${box.read('token')}',
        'Content-Type': 'application/json', // optional, depends on API
      },
    ),);

    if (response.statusCode == 200) {
      final List<dynamic> productsJson = response.data['data'];
      final products = productsJson.map((e) => Product.fromJson(e)).toList();

      // Sort products by price (high to low)
      products.sort((a, b) => b.price.compareTo(a.price));

      return products;
    } else {
      throw Exception('Failed to load products');
    }
  } catch (e) {
    print('Error: $e');
    return [];
  }
}
Future<List<Product>> fetchProductsLow() async {
  try {
    final dio = Dio();
    final response = await dio.get('https://project-yhx7.onrender.com/api/v1/products/wishlist-compare',options: Options(
      headers: {
        'Authorization': 'Bearer ${box.read('token')}',
        'Content-Type': 'application/json', // optional, depends on API
      },
    ),);

    if (response.statusCode == 200) {
      final List<dynamic> productsJson = response.data['data'];
      final products = productsJson.map((e) => Product.fromJson(e)).toList();

      // Sort products by price (low to high)
      products.sort((a, b) => a.price.compareTo(b.price));

      return products;
    } else {
      throw Exception('Failed to load products');
    }
  } catch (e) {
    print('Error: $e');
    return [];
  }
}
Future<List<Product>> fetchProductsfav() async {
  try {
    final dio = Dio();
    final response = await dio.get(
      'https://project-yhx7.onrender.com/api/v1/products/wishlist-compare',
      options: Options(
        headers: {
          'Authorization': 'Bearer ${box.read('token')}',
          'Content-Type': 'application/json',
        },
      ),
    );

    if (response.statusCode == 200) {
      print(response.data);
      final List<dynamic> productsJson = response.data['data'];
      return productsJson
          .map((e) => Product.fromJson(e))
          .where((product) => product.isWishlisted == true)
          .toList();
    } else {
      throw Exception('Failed to load products');
    }
  } catch (e) {
    print('Error: $e');
    return [];
  }
}





