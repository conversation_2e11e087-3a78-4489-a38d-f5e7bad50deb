import 'package:flutter/material.dart';

class SizeOptionSelector extends StatelessWidget {
  final List<String> sizes; // e.g. ['M', 'L', 'XL', 'XXL']

  const SizeOptionSelector({super.key, required this.sizes});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 16),
      child: Row(
        children: sizes.map((size) {
          return Padding(
            padding: const EdgeInsets.only(right: 16), // space between items
            child: Container(
              width: 50,
              height: 45,
              decoration: BoxDecoration(
                color: const Color(0xFF263D54), // from your JSON rgba(38, 61, 84)
                borderRadius: BorderRadius.circular(10),
                border: Border.all(color: Colors.white),
              ),
              child: Center(
                child: Text(
                  size,
                  style: const TextStyle(
                    fontFamily: 'Cairo',
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,

                  ),

                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}
