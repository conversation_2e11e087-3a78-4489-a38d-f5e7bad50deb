import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

import '../Acconts/CreateAccPage.dart';
import '../MainScaffoldPage.dart';

class CreateAccountCardWithBackground extends StatelessWidget {
  const CreateAccountCardWithBackground({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(

        height: 350,
        child: Stack(
          children: [
            // Background Image with rounded top corners
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
              child: Image.asset(
                'assets/background.jpg', // Replace with your image path

                height: 350,
                fit: BoxFit.cover,
              ),
            ),

            // Foreground content over the image
            Container(
              width: 1000,
              height: 350,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
                color: Colors.black.withOpacity(0.4), // Optional dark overlay for contrast
              ),
              child: Padding(
                padding: EdgeInsets.only(left:10),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Align(
                      alignment: Alignment.centerLeft,
                      child: const Text(
                        "Create your account now and enjoy many features .",
                        style: TextStyle(
                          fontSize: 16,
                          fontFamily: 'Cairo',
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Align(
                      alignment: Alignment.centerLeft,
                      child: const Text(
                        "Create orders faster",
                        style: TextStyle(
                          fontSize: 18,
                          fontFamily: 'Cairo',

                          color: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Align(
                      alignment: Alignment.centerLeft,
                      child: const Text(
                        "Track your order easily",
                        style: TextStyle(
                          fontSize: 18,
                          fontFamily: 'Cairo',

                          color: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Align(
                      alignment: Alignment.centerLeft,
                      child: const Text(
                        "Add different shipping addresses",
                        style: TextStyle(
                          fontSize: 18,
                          fontFamily: 'Cairo',

                          color: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Align(
                      alignment: Alignment.centerLeft,
                      child: const Text(
                        "Don't have an account ?",
                        style: TextStyle(
                          fontSize: 18,
                          fontFamily: 'Cairo',

                          color: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(height: 30),
                    GestureDetector(
                      onTap: (){
                        Navigator.of(context).push(
                          MaterialPageRoute(builder: (context) => CreateAccPage()),
                        );
                      },
                      child: Container(
                        width: 200,
                        height: 60,
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.white),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Center(
                          child: Text(
                            "Create an Account",
                            style: TextStyle(
                              fontSize: 20,
                              fontFamily: 'Cairo',
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
