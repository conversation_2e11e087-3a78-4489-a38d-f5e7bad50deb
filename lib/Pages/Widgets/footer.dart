import 'package:flutter/material.dart';

class AppFooter extends StatelessWidget {
  const AppFooter({Key? key}) : super(key: key);

  static const backgroundColor = Color(0xFF263C54);
  static const textColor = Color(0xFFD4D1D1);
  static const logoFontFamily = 'Major Mono Display';
  static const bodyFontFamily = 'Cairo';

  @override
  Widget build(BuildContext context) {
    return Container(
      // width: 428,
      color: backgroundColor,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Logo (can be clickable too)
          Center(
            child: GestureDetector(
              onTap: () {
                print('Logo clicked');
                // TODO: add your navigation logic here
              },
              child: Container(
                width: 145,
                height: 100,
                padding: const EdgeInsets.all(10),
                alignment: Alignment.center,
                child: Text.rich(
                  TextSpan(
                    children: [
                      TextSpan(
                        text: 'M',
                        style: TextStyle(color: Colors.white),
                      ),
                      TextSpan(
                        text: 'E',
                        style: TextStyle(color: Color(0xFFFFA726)),
                      ),
                      TextSpan(
                        text: 'N',
                        style: TextStyle(color: Colors.white),
                      ),
                      TextSpan(
                        text: 'G',
                        style: TextStyle(color: Color(0xFFFFA726)),
                      ),
                    ],
                    style: TextStyle( // base style shared by all spans
                      fontFamily: 'Major Mono Display',
                      fontSize: 40,
                    ),
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
          const SizedBox(height: 24),

          // Info and Links Row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Contact Info
              _ClickableFooterColumn(
                items: const [
                  '+201200320731',
                  '<EMAIL>',
                  'Egypt, Ismailia\n104 St Shebin Elkom',
                ],
                onItemTap: (text) {
                  print('Contact Info clicked: $text');
                  // TODO: add your logic (e.g., launch dialer/email/map)
                },
              ),

              // Navigation Links
              _ClickableFooterColumn(
                items: const [
                  'About Us',
                  'Products',
                  'Privacy policy',
                  'Contact',
                ],
                onItemTap: (text) {
                  print('Navigation Link clicked: $text');
                  // TODO: Navigate to the respective page
                },
              ),

              // Social Links
              _ClickableFooterColumn(
                items: const [
                  'Facebook',
                  'Twitter',
                  'Linkedin',
                  'Instagram',
                ],
                onItemTap: (text) {
                  print('Social Link clicked: $text');
                  // TODO: Open social media URL
                },
              ),
            ],
          ),

          const SizedBox(height: 32),

          // Up Arrow Indicator (clickable to scroll to top maybe)
          GestureDetector(
            onTap: () {
              print('Scroll to top clicked');
              // TODO: implement scroll to top
            },
            child: SizedBox(
              width: 40,
              height: 32,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Transform.rotate(
                    angle: -1.5708,
                    child: Container(
                      width: 32,
                      height: 40,
                      decoration: BoxDecoration(
                        color: const Color(0xFF0081FE),
                        borderRadius: BorderRadius.circular(100),
                      ),
                    ),
                  ),
                  const Icon(
                    Icons.keyboard_arrow_up,
                    size: 24,
                    color: Colors.white,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Copyright text (optional clickable)
          GestureDetector(
            onTap: () {
              print('Copyright clicked');
              // Optional: maybe open a website or license
            },
            child: Text(
              '© copyright by Momen Abdelrazik 2024',
              style: const TextStyle(
                color: textColor,
                fontSize: 16,
                fontFamily: bodyFontFamily,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.left,
            ),
          ),
        ],
      ),
    );
  }
}

class _ClickableFooterColumn extends StatelessWidget {
  final List<String> items;
  final void Function(String text) onItemTap;

  const _ClickableFooterColumn({
    required this.items,
    required this.onItemTap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: items
          .map(
            (text) => Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: InkWell(
            onTap: () => onItemTap(text),
            child: Text(
              text,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                fontFamily: 'Cairo',
                color: Color(0xFFD4D1D1),
                decoration: TextDecoration.underline, // optional: to show it's clickable
              ),
            ),
          ),
        ),
      )
          .toList(),
    );
  }
}
