import 'package:flutter/material.dart';

class CustomHeader2 extends StatefulWidget {

  const CustomHeader2({super.key});

  @override
  State<CustomHeader2> createState() => _CustomHeader2State();
}

class _CustomHeader2State extends State<CustomHeader2> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          width: double.infinity,
          height: 60,
          color: const Color.fromRGBO(38, 61, 84, 1), // r=0.149, g=0.239, b=0.329
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child:

              Stack(
                alignment: Alignment.center,
                children: [
                  /// Left Icon
                  const Align(
                    alignment: Alignment.centerLeft,
                    child: Icon(Icons.menu_open, size: 24, color: Colors.white),
                  ),

                  /// Logo Text
                  Container(
                    margin: const EdgeInsets.only(left: 15),
                    padding: const EdgeInsets.all(0),
                    width: 139,
                    height: 100,
                    child: const Center(
                        child: Text.rich(
                          TextSpan(
                            children: [
                              TextSpan(
                                text: 'M',
                                style: TextStyle(color: Colors.white),
                              ),
                              TextSpan(
                                text: 'E',
                                style: TextStyle(color: Color(0xFFFFA726)),
                              ),
                              TextSpan(
                                text: 'N',
                                style: TextStyle(color: Colors.white),
                              ),
                              TextSpan(
                                text: 'G',
                                style: TextStyle(color: Color(0xFFFFA726)),
                              ),
                            ],
                            style: TextStyle( // base style shared by all spans
                              fontFamily: 'Major Mono Display',
                              fontSize: 40,
                            ),
                          ),
                          textAlign: TextAlign.center,
                        ),
                    ),
                  ),

                  /// Right Icon
                  const Align(
                    alignment: Alignment.centerRight,
                    child: Icon(Icons.settings, size: 24, color: Colors.white),
                  ),
                ],
              ),



        ),

      ],
    );
  }
}
