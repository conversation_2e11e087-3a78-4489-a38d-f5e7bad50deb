import 'package:flutter/material.dart';
import 'package:mengv2/Pages/MainScaffoldPage.dart';
import 'package:webview_flutter/webview_flutter.dart';



class StripePaymentScreen extends StatefulWidget {
  final String checkoutUrl; // Stripe checkout/session URL
  final String successUrl;  // must EXACTLY match <PERSON><PERSON>'s success_url

  const StripePaymentScreen({
    super.key,
    required this.checkoutUrl,
    required this.successUrl,
  });

  @override
  State<StripePaymentScreen> createState() => _StripePaymentScreenState();
}

class _StripePaymentScreenState extends State<StripePaymentScreen> {
  late final WebViewController _controller;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();

    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageFinished: (_) => setState(() => _isLoading = false),
          onNavigationRequest: (NavigationRequest request) {
            if (request.url.startsWith(widget.successUrl)) {
              // ✅ Payment finished – navigate to success screen
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(builder: (_) =>  MainScaffoldPage()),
              );
              return NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.checkoutUrl));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Secure Payment')),
      body: Stack(
        children: [
          WebViewWidget(controller: _controller),
          if (_isLoading)
            const Center(child: CircularProgressIndicator()),
        ],
      ),
    );
  }
}
