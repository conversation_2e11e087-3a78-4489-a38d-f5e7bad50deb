import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:mengv2/Pages/Products/ProductsPage.dart';

import 'navigation_controller.dart';

class CustomDrawer extends StatefulWidget {
  const CustomDrawer({super.key});

  @override
  State<CustomDrawer> createState() => _CustomDrawerState();
}

class _CustomDrawerState extends State<CustomDrawer> {
  int selectedIndex = -1;
final box =GetStorage();
  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: const Color(0xFF3E5D7B),
      child: Column(
        children: [
          const SizedBox(height: 40),

          /// MENG Text
          Center(
            child: Text.rich(
              TextSpan(
                children: const [
                  TextSpan(text: 'M', style: TextStyle(color: Colors.white)),
                  TextSpan(text: 'E', style: TextStyle(color: Color(0xFFFFA726))),
                  TextSpan(text: 'N', style: TextStyle(color: Colors.white)),
                  TextSpan(text: 'G', style: TextStyle(color: Color(0xFFFFA726))),
                ],
                style: TextStyle(
                  fontFamily: 'Major Mono Display',
                  fontSize: 36,
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          /// Circular Image
           CircleAvatar(
            radius: 40,
            backgroundImage: NetworkImage(box.read('image')), // Replace with your image path
          ),

          const SizedBox(height: 10),

          /// Two Texts
           Text(
             box.read('name')
            ,
            style: TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold),
          ),
           Text(
            box.read('email'),
            style: TextStyle(color: Colors.white70, fontSize: 14),
          ),

          const Padding(
            padding: EdgeInsets.symmetric(vertical: 16),
            child: Divider(color: Colors.white24, thickness: 1, indent: 20, endIndent: 20),
          ),

          /// Drawer Buttons
          drawerButton(
            index: 0,
            icon: Icons.home,
            label: 'Home',
          ),
          drawerButton(
            index: 1,
            icon: Icons.shopping_basket_outlined,
            label: 'Products',
          ),
          drawerButton(
            index: 2,
            icon: Icons.phone,
            label: 'Contact US',
          ),
          drawerButton(
            index: 3,
            icon: Icons.contact_support_outlined,
            label: 'About us',
          ),
          drawerButton(
            index: 4,
            icon: Icons.privacy_tip_outlined,
            label: 'Privacy Policy',
          ),
        ],
      ),
    );
  }

  Widget drawerButton({required int index, required IconData icon, required String label}) {
    final bool isSelected = selectedIndex == index;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      child: GestureDetector(
        onTap: () {
          setState(() {
            selectedIndex = index;
          });
          if(selectedIndex==0){
            currentIndexNotifier.value = 0;


          }else if(selectedIndex==1){
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => ProductPage()),
            );
          }

          // Optionally close the drawer
          // Navigator.pop(context);
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: isSelected ? Colors.white.withOpacity(0.15) : Colors.transparent,
            borderRadius: BorderRadius.circular(12),
            border: isSelected ? Border.all(color: Colors.white, width: 1.5) : null,
          ),
          child: Row(
            children: [
              Icon(icon, color: Colors.white),
              const SizedBox(width: 12),
              Text(
                label,
                style: const TextStyle(color: Colors.white, fontSize: 16),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
