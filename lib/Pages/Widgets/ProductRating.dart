import 'package:flutter/material.dart';

class ProductRatingRow extends StatelessWidget {
  final double rating; // From 0.0 to 5.0
  final int reviewCount;
  final double price;

  const ProductRatingRow({
    super.key,
    required this.price,
    required this.rating,
    required this.reviewCount,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 16),
      child: SizedBox(
        height: 20,
        child: Row(
          children: [
            // Left: Price
            SizedBox(
              width: 89,
              child: Text(
                "${price} EGP",
                style: TextStyle(
                  fontFamily: 'Cairo',
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                  height: 20 / 22,
                ),
                textAlign: TextAlign.left,
              ),
            ),
            const SizedBox(width: 20),

            // Right: Stars, Review count, "Ratings"
            Row(
              children: [
                // ⭐ Dynamic stars
                Row(
                  children: List.generate(5, (index) {
                    if (rating >= index + 1) {
                      return const Icon(Icons.star, size: 20, color: Colors.amber);
                    } else if (rating > index && rating < index + 1) {
                      return const Icon(Icons.star_half, size: 20, color: Colors.amber);
                    } else {
                      return const Icon(Icons.star_border, size: 20, color: Colors.amber);
                    }
                  }),
                ),
                const SizedBox(width: 6),

                // (21)
                Text(
                  '($reviewCount)',
                  style: const TextStyle(
                    fontFamily: 'Cairo',
                    fontSize: 14,
                    fontWeight: FontWeight.normal,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(width: 6),

                // Ratings label
                const Text(
                  'Ratings',
                  style: TextStyle(
                    fontFamily: 'Cairo',
                    fontSize: 14,
                    fontWeight: FontWeight.normal,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
