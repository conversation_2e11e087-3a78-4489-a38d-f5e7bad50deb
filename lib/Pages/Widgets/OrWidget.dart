import 'package:flutter/material.dart';

class OrDivider extends StatelessWidget {
  const OrDivider({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        const SizedBox(width: 16), // Match ml-16 from Figma

        // Left Line
        const Expanded(
          child: Divider(
            color: Colors.black,
            thickness: 1,
            height: 1,
          ),
        ),

        // Spacing and OR text
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 10),
          child: Text(
            'OR',
            style: TextStyle(
              fontSize: 16,
              fontFamily: 'Cairo',
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
        ),

        // Right Line
        const Expanded(
          child: Divider(
            color: Colors.black,
            thickness: 1,
            height: 1,
          ),
        ),

        const SizedBox(width: 16), // To match symmetry on right side
      ],
    );
  }
}
