import 'dart:math';
import 'dart:ui';

import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get_storage/get_storage.dart';

import '../../Models/Products/ProductModel.dart';
import '../ProductDetails/ProductDetailsPage.dart';
import 'package:mengv2/globals.dart';


class ProductGrid extends StatelessWidget {
  final List<Product> products;
  final int limits;
  final bool favordelete;
  final Function(String)? onRemove;

  const ProductGrid({super.key, required this.products,required this.limits,required this.favordelete, required this.onRemove});

  @override
  Widget build(BuildContext context) {
    final limitedProducts = products.take(limits).toList(); // Max 6 items

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: limitedProducts.length,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        mainAxisSpacing: 12,
        crossAxisSpacing: 12,
        childAspectRatio: 190 / 240,
      ),
      itemBuilder: (context, index) {
        return ProductCard(product: limitedProducts[index],favordelete: favordelete,onRemove: onRemove);
      },
    );
  }
}

class ProductCard extends StatefulWidget {
  final Product product;
  final bool favordelete;
  final Function(String)? onRemove;

  const ProductCard({super.key, required this.product,required this.favordelete, required this.onRemove});

  @override
  State<ProductCard> createState() => _ProductCardState();
}

class _ProductCardState extends State<ProductCard> {

  double roundToDecimals(double value, int places) {
    double mod = pow(10.0, places).toDouble();
    return (value * mod).round() / mod;
  }
  final Dio _dio = Dio();
  final box=GetStorage();
  late Color favcolor;
  late bool  Wishlisted;

  @override
  void initState() {
    super.initState();
    favcolor = widget.product.isWishlisted! ? Colors.red : Colors.white;
    Wishlisted=widget.product.isWishlisted!;
  }



  Future<void> remove() async {
    var response;
    try {
      response = await _dio.delete(
        'https://project-yhx7.onrender.com/api/v1/wishlist/${widget.product.id.toString()}', // Replace with your API URL
        data: {

        },
        options: Options(
          headers: {
            'Authorization': 'Bearer ${box.read('token')}',
            'Content-Type': 'application/json', // optional, depends on API
          },
        ),
      );

      if (response.statusCode == 200 ) {
        widget.onRemove!(widget.product.id);
        Fluttertoast.showToast(msg:  'Removed Successful');
        setState(() {
          Wishlisted=false;
          favcolor=Colors.white;
        });


      } else {
        Fluttertoast.showToast(msg: response.data?? 'Error in remove from whitelist');
      }
    } catch (e) {
      Fluttertoast.showToast(msg: 'Error: ${e.toString()}');
    }



  }
  Future<void> addtofav() async {
    if(Wishlisted){
      var response;
      try {
        response = await _dio.delete(
          'https://project-yhx7.onrender.com/api/v1/wishlist/${widget.product.id.toString()}', // Replace with your API URL
          data: {

          },
          options: Options(
            headers: {
              'Authorization': 'Bearer ${box.read('token')}',
              'Content-Type': 'application/json', // optional, depends on API
            },
          ),
        );

        if (response.statusCode == 200 ) {
          Fluttertoast.showToast(msg:  'Removed Successful');
          setState(() {
            Wishlisted=false;
            favcolor=Colors.white;
          });


        } else {
          Fluttertoast.showToast(msg: response.data?? 'Error in remove from whitelist');
        }
      } catch (e) {
        Fluttertoast.showToast(msg: 'Error: ${e.toString()}');
      }

    }else{
      var response;
      try {
        response = await _dio.post(
          'https://project-yhx7.onrender.com/api/v1/wishlist/${widget.product.id.toString()}', // Replace with your API URL
          data: {

          },
          options: Options(
            headers: {
              'Authorization': 'Bearer ${box.read('token')}',
              'Content-Type': 'application/json', // optional, depends on API
            },
          ),
        );

        if (response.statusCode == 200 ) {
          Fluttertoast.showToast(msg:  'Added Successful');
          setState(() {
            Wishlisted=true;
            favcolor=Colors.red;
          });


        } else {
          Fluttertoast.showToast(msg: response.data?? 'Error in add to whitelist');
        }
      } catch (e) {
        Fluttertoast.showToast(msg: 'Error: ${e.toString()}');
      }
    }




  }
  Future<void> addtoCart() async {

      var response;
      try {
        response = await _dio.post(
          'https://project-yhx7.onrender.com/api/v1/cart/add', // Replace with your API URL
          data: {
            "productId": widget.product.id
          },
          options: Options(
            headers: {
              'Authorization': 'Bearer ${box.read('token')}',
              'Content-Type': 'application/json', // optional, depends on API
            },
          ),
        );

        if (response.statusCode == 200 ) {
          Fluttertoast.showToast(msg:  'Added Successful');



        } else {
          Fluttertoast.showToast(msg: response.data?? 'Error Adding to Cart');
        }
      } catch (e) {
        Fluttertoast.showToast(msg: 'Error: ${e.toString()}');
      }






  }

  @override
  Widget build(BuildContext context) {


      return Container(
        width: 190,
        height: 240,
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: const Color(0xFF263D54)),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Stack(
          children: [
            Column(
              children: [
                GestureDetector(
                  onTap: (){
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => ProductDetails(product: widget.product,)),
                    );
                  },
                  child: Container(
                    width: double.infinity,
                    height: 138,
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: NetworkImage(widget.product.imageCover),
                        fit: BoxFit.cover,
                      ),
                      borderRadius: const BorderRadius.vertical(top: Radius.circular(10)),
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: (){
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => ProductDetails(product: widget.product,)),
                    );
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(5),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Expanded(  // <-- add this
                          child: Text(
                            widget.product.title,
                            style: const TextStyle(
                              fontFamily: 'Cairo',
                              fontWeight: FontWeight.w500,
                              fontSize: 16,
                              height: 22 / 16,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.center,  // optional, to keep text centered
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                GestureDetector(
                  onTap: (){
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => ProductDetails(product: widget.product,)),
                    );
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 5),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          widget.product.price.toString(),
                          style: const TextStyle(
                            fontFamily: 'Cairo',
                            fontWeight: FontWeight.w500,
                            fontSize: 16,
                            height: 22 / 16,
                          ),
                        ),
                        Row(
                          children: [
                            const Icon(Icons.star, color: Color(0xFFFFD700), size: 16),
                            const SizedBox(width: 4),
                            Text(
                                roundToDecimals(widget.product.ratingsAverage, 1).toString(),
                              style: const TextStyle(
                                fontFamily: 'Cairo',
                                fontWeight: FontWeight.w500,
                                fontSize: 14,
                                height: 22 / 14,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                const Spacer(),

                GestureDetector(
                  onTap: (){
                    addtoCart();
                  },
                  child: Container(
                    width: double.infinity,
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    decoration: BoxDecoration(
                      color: Colors.black,
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: Colors.black),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: const Center(
                      child: Text(
                        'ADD TO CART',
                        style: TextStyle(
                          fontFamily: 'Cairo',
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                          height: 22 / 16,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 5),
              ],
            ),
        widget.favordelete?
        Positioned(
              top: 4,
              right: 4,
              child: GestureDetector(
                onTap: (){
                  addtofav();
                },
                child: Container(
                  width: 30,
                  height: 30,
                  decoration:  BoxDecoration(
                    shape: BoxShape.circle,
                    color: favcolor,
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.favorite_border,
                      size: 20,
                      color: Color(0xFF263D54),
                    ),
                  ),
                ),
              ),
            )
            :Positioned(
          top: 4,
          right: 4,
          child: GestureDetector(
            onTap: (){
              remove();
            },
            child: Container(
              width: 30,
              height: 30,
              decoration:  BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white,
              ),
              child: const Center(
                child: Icon(
                  Icons.delete,
                  size: 20,
                  color: Color(0xFF263D54),
                ),
              ),
            ),
          ),
        ),
          ],
        ),
      );

  }
}
