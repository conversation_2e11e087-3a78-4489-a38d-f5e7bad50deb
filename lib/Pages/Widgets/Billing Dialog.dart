import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get_storage/get_storage.dart';

import 'StripePaymentScreen.dart';

class BillingAddressDialog extends StatefulWidget {
  final String OrderID;
  const BillingAddressDialog({super.key,required this.OrderID});

  @override
  State<BillingAddressDialog> createState() => _BillingAddressDialogState();
}

class _BillingAddressDialogState extends State<BillingAddressDialog> {
  final _dio=Dio();
  final box=GetStorage();
  void Pay()async{
    var response;

    try {
      response = await _dio.post(
        'https://project-yhx7.onrender.com/api/v1/orders/checkout-session/${widget.OrderID}', // Replace with your API URL
        data: {
          "shippingAddress": {
            "address": streetCtrl.text.toString(),
            "city": selectedCity.toString(),
            "state": selectedArea.toString(),
            "street": streetCtrl.text.toString(),
            "country": "Egypt",
            "phone": phoneCtrl.text.toString()
          }
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer ${box.read('token')}',
            'Content-Type': 'application/json', // optional, depends on API
          },
        ),
      );

      if (response.statusCode == 200 ) {
        Fluttertoast.showToast(msg:  'processing to payment....');
        box.write('payurl', response.data['session']['url']);
        print(response.data['session']['url']);
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (_) => StripePaymentScreen(
              checkoutUrl: response.data['session']['url'],     // e.g. from backend
              successUrl: 'https://project-yhx7.onrender.com/orders/success',
            ),
          ),
        );




      } else {
        Fluttertoast.showToast(msg: response.data?? 'Error');
      }
    } catch (e) {
      Fluttertoast.showToast(msg: 'Error: In Payment Be Sure That you fill all Shipping Address');
    }
  }
  final firstNameCtrl = TextEditingController();
  final lastNameCtrl = TextEditingController();
  final streetCtrl = TextEditingController();
  final phoneCtrl = TextEditingController();

  String? selectedCity;
  String? selectedArea;
  bool saveCompany = false;

  final Map<String, List<String>> egyptCitiesWithAreas = {
    "Cairo": [
      "Nasr City",
      "Heliopolis",
      "Maadi",
      "Zamalek",
      "New Cairo",
      "Shubra",
      "Downtown",
      "Mohandessin",
      "6th October",
    ],
    "Giza": [
      "Dokki",
      "Agouza",
      "Haram",
      "Faisal",
      "Sheikh Zayed",
      "6th October",
      "Warraq",
      "Imbaba",
    ],
    "Alexandria": [
      "Smouha",
      "Sidi Gaber",
      "Stanley",
      "Gleem",
      "Miami",
      "Montazah",
      "Sporting",
    ],
    "Aswan": ["Aswan City", "Edfu", "Kom Ombo", "Daraw"],
    "Luxor": ["Luxor City", "Karnak", "Armant", "Esna"],
    "Assiut": ["Assiut City", "Manfalut", "Abnub", "Dairut"],
    "Minya": ["Minya City", "Mallawi", "Beni Mazar", "Samalut"],
    "Qena": ["Qena City", "Nag Hammadi", "Qus", "Dishna"],
    "Sohag": ["Sohag City", "Tahta", "Akhmim", "Gerga"],
    "Ismailia": ["Ismailia City", "Qantara East", "Qantara West", "Fayed"],
    "Port Said": ["Port Said City", "Port Fouad"],
    "Suez": ["Suez City", "Ain Sokhna"],
    "Beni Suef": ["Beni Suef City", "Nasser", "Biba", "Al Fashn"],
    "Fayoum": ["Fayoum City", "Sinnuris", "Ibshaway", "Tamiya"],
    "Kafr El Sheikh": ["Kafr El Sheikh City", "Desouk", "Baltim", "Fouwa"],
    "Beheira": ["Damanhour", "Kafr El Dawar", "Rashid", "Itay El Barud"],
    "Dakahlia": ["Mansoura", "Talkha", "Mit Ghamr", "Dikirnis"],
    "Sharqia": ["Zagazig", "Bilbeis", "Abu Hammad", "10th of Ramadan"],
    "Gharbia": ["Tanta", "El Mahalla El Kubra", "Kafr El Zayat", "Zefta"],
    "Monufia": ["Shebin El Kom", "Ashmoun", "Quesna", "Menouf"],
    "Qalyubia": ["Banha", "Shubra El Kheima", "Qalyub", "Khosous"],
    "Red Sea": ["Hurghada", "Safaga", "El Quseir", "Marsa Alam"],
    "Matrouh": ["Marsa Matruh", "Siwa", "El Alamein", "Dabaa"],
    "North Sinai": ["Arish", "Sheikh Zuweid", "Rafah"],
    "South Sinai": ["Sharm El Sheikh", "Dahab", "Nuweiba", "Saint Catherine"],
    "New Valley": ["Kharga", "Dakhla", "Farafra", "Baris"],
  };


  @override
  void dispose() {
    firstNameCtrl.dispose();
    lastNameCtrl.dispose();
    streetCtrl.dispose();
    phoneCtrl.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    const borderColor = Color(0xFF0988FF);
    const bgColor = Color(0xFF263D54);

    InputDecoration inputDec(String hint) => InputDecoration(
      hintText: hint,
      hintStyle: const TextStyle(
        fontSize: 14,
        fontFamily: 'Cairo',
        color: Colors.black54,
      ),
      filled: true,
      fillColor: Colors.white,
      contentPadding: const EdgeInsets.symmetric(vertical: 12, horizontal: 10),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: borderColor),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: borderColor, width: 2),
      ),
    );

    Widget label(String text) => Padding(
      padding: const EdgeInsets.only(bottom: 4, left: 2),
      child: Text(
        text,
        style: const TextStyle(
          fontFamily: 'Cairo',
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
    );

    return Dialog(
      backgroundColor: bgColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(25)),
      child: Container(
        width: 396,
        padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
        decoration: BoxDecoration(
          border: Border.all(color: borderColor),
          borderRadius: BorderRadius.circular(25),
        ),
        child: SingleChildScrollView(
          child: Column(
            children: [
              // First Name & Last Name
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        label('First Name'),
                        TextField(controller: firstNameCtrl, decoration: inputDec('first name')),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        label('Last Name'),
                        TextField(controller: lastNameCtrl, decoration: inputDec('last name')),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // Street Address & City
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        label('Street Address'),
                        TextField(controller: streetCtrl, decoration: inputDec('Street')),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        label('City'),
                        DropdownButtonFormField<String>(
                          value: selectedCity,
                          decoration: inputDec('Select city'),
                          items: egyptCitiesWithAreas.keys
                              .map((city) => DropdownMenuItem(
                            value: city,
                            child: Text(city),
                          ))
                              .toList(),
                          onChanged: (city) {
                            setState(() {
                              selectedCity = city;
                              selectedArea = null;
                            });
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // Area & Phone
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        label('Area'),
                        DropdownButtonFormField<String>(
                          value: selectedArea,
                          decoration: inputDec('Select Area'),
                          items: selectedCity != null
                              ? egyptCitiesWithAreas[selectedCity!]!
                              .map((area) => DropdownMenuItem(
                            value: area,
                            child: Text(area),
                          ))
                              .toList()
                              : [],
                          onChanged: (area) => setState(() => selectedArea = area),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        label('Phone'),
                        TextField(
                          controller: phoneCtrl,
                          keyboardType: TextInputType.phone,
                          decoration: inputDec('+20'),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // Save Company Checkbox
              Row(
                children: [
                  Checkbox(
                    value: saveCompany,
                    activeColor: borderColor,
                    onChanged: (v) => setState(() => saveCompany = v!),
                  ),
                  const SizedBox(width: 4),
                  const Expanded(
                    child: Text(
                      'Save Company Details (Optional)',
                      style: TextStyle(
                        fontSize: 14,
                        fontFamily: 'Cairo',
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Submit Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: borderColor,
                    padding: const EdgeInsets.symmetric(vertical: 14),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  onPressed: () {
                    // TODO: Validate and process
                    // Navigator.of(context).pop();
                    Pay();
                  },
                  child: const Text(
                    'Process to Payment',
                    style: TextStyle(
                      fontSize: 16,
                      fontFamily: 'Cairo',
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
