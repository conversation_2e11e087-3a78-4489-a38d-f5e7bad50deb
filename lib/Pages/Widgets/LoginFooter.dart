import 'package:flutter/material.dart';
import 'package:mengv2/Pages/Acconts/LoginPage.dart';

class LoginCardWithBackground extends StatelessWidget {
  const LoginCardWithBackground({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(

        height: 300,
        child: Stack(
          children: [
            // Background Image with rounded top corners
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
              child: Image.asset(
                'assets/background.jpg', // Replace with your image path

                height: 300,
                fit: BoxFit.cover,
              ),
            ),

            // Foreground content over the image
            Container(
              width: 1000,
              height: 300,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
                color: Colors.black.withOpacity(0.4), // Optional dark overlay for contrast
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    "You have an account?",
                    style: TextStyle(
                      fontSize: 16,
                      fontFamily: 'Cairo',
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    "Log in into your account",
                    style: TextStyle(
                      fontSize: 18,
                      fontFamily: 'Cairo',
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 30),
                  GestureDetector(
    onTap: (){
    Navigator.of(context).push(
    MaterialPageRoute(builder: (context) => LoginPage()),
    );},
                    child: Container(
                      width: 200,
                      height: 60,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.white),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Center(
                        child: Text(
                          "Log in",
                          style: TextStyle(
                            fontSize: 20,
                            fontFamily: 'Cairo',
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
