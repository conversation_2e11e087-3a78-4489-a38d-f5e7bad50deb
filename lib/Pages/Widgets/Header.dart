import 'package:flutter/material.dart';

class CustomHeader extends StatelessWidget {
  final String title;

  const CustomHeader({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          width: double.infinity,
          height: 60,
          color: const Color.fromRGBO(38, 61, 84, 1),
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Stack(
            alignment: Alignment.center,
            children: [
              /// Left Icon – opens drawer using Scaffold.of(context)
              Align(
                alignment: Alignment.centerLeft,
                child: Builder(
                  builder: (context) => IconButton(
                    icon: const Icon(Icons.menu, color: Colors.white),
                    onPressed: () {
                      Scaffold.of(context).openDrawer();
                    },
                  ),
                ),
              ),

              /// Logo
              Container(
                margin: const EdgeInsets.only(left: 15),
                width: 139,
                height: 100,
                child: const Center(
                  child: Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(text: 'M', style: TextStyle(color: Colors.white)),
                        TextSpan(text: 'E', style: TextStyle(color: Color(0xFFFFA726))),
                        TextSpan(text: 'N', style: TextStyle(color: Colors.white)),
                        TextSpan(text: 'G', style: TextStyle(color: Color(0xFFFFA726))),
                      ],
                      style: TextStyle(
                        fontFamily: 'Major Mono Display',
                        fontSize: 40,
                      ),
                    ),
                  ),
                ),
              ),

              /// Right Icon
              const Align(
                alignment: Alignment.centerRight,
                child: Icon(Icons.settings, color: Colors.white),
              ),
            ],
          ),
        ),

        /// Title Section
        Container(
          width: 370,
          height: 50,
          padding: const EdgeInsets.symmetric(vertical: 4),
          decoration: BoxDecoration(
            color: const Color(0xFF024282),
            border: Border.all(color: Color(0xFF263D54), width: 1),
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(10),
              bottomRight: Radius.circular(10),
            ),
          ),
          child: Center(
            child: Text(
              title,
              style: const TextStyle(
                fontFamily: 'Cairo',
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
