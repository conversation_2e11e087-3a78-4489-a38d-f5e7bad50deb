import 'package:flutter/material.dart';

class UpdatesBanner extends StatefulWidget {
  const UpdatesBanner({super.key});

  @override
  State<UpdatesBanner> createState() => _UpdatesBannerState();
}

class _UpdatesBannerState extends State<UpdatesBanner> {
  final TextEditingController _emailController = TextEditingController();

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  void _handleSubscribe() {
    final email = _emailController.text.trim();
    // TODO: Add your logic (validation or API call)
    debugPrint('Subscribed with: $email');
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      // width: 428,
      height: 140,
      color: const Color(0xFF1D2934), // Background color
      child: Stack(
        children: [
          // Title
          const Positioned(
            left: 50,
            top: 20,
            child: Text(
              "Don't Miss Our Updates",
              style: TextStyle(
                fontSize: 28,
                color: Colors.white,
                fontFamily: 'Cairo',
                fontWeight: FontWeight.w600,
              ),
            ),
          ),

          // Email TextField
          Positioned(
            left: 50,
            top: 71,
            child: Si<PERSON><PERSON><PERSON>(
              width: 210,
              height: 35,
              child: TextField(
                controller: _emailController,
                style: const TextStyle(
                  fontFamily: 'Cairo',
                  fontSize: 14,
                ),
                decoration: InputDecoration(
                  contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                  hintText: 'Email Address',
                  hintStyle: const TextStyle(
                    color: Colors.black54,
                    fontWeight: FontWeight.w600,
                  ),
                  filled: true,
                  fillColor: const Color(0xFFD9D9D9),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(5),
                    borderSide: const BorderSide(color: Color(0xFF0B92F5)),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(5),
                    borderSide: const BorderSide(color: Color(0xFF0B92F5)),
                  ),
                ),
              ),
            ),
          ),

          // Subscribe Button
          Positioned(
            left: 280,
            top: 72,
            child: SizedBox(
              width: 80,
              height: 35,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF263C54),
                  foregroundColor: Colors.white,
                  textStyle: const TextStyle(
                    fontSize: 14,
                    fontFamily: 'Cairo',
                    fontWeight: FontWeight.bold,
                  ),
                  padding: EdgeInsets.zero,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(5),
                    side: const BorderSide(color: Color(0xFF0B92F5)),
                  ),
                ),
                onPressed: _handleSubscribe,
                child: const Text('Subscribe'),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
