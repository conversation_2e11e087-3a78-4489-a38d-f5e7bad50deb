import 'package:carousel_slider/carousel_controller.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get_storage/get_storage.dart';

import '../../Models/Products/ProductModel.dart';


import '../../globals.dart';
import '../Widgets/HeaderStyle2.dart';
import '../Widgets/ProductRating.dart';
import '../Widgets/Sizes.dart';

class ProductDetails extends StatefulWidget {
  final Product product;
  const ProductDetails({super.key, required this.product});

  @override
  State<ProductDetails> createState() => _ProductDetailsState();
}

class _ProductDetailsState extends State<ProductDetails> {
  final Dio _dio = Dio();
  final box=GetStorage();
  late Color favcolor;
  late bool Wishlisted;
  late String SelectedSize;
  late  int quantity;
  late int productquantity;
  @override
  void initState() {
    super.initState();
    favcolor = widget.product.isWishlisted! ? Colors.red : Colors.white;
    Wishlisted=widget.product.isWishlisted!;
    if(widget.product.quantity>0){
      productquantity=widget.product.quantity-1;
      quantity=1;
    }else{
      quantity=0;
    }

  }
  Future<void> addtofav() async {
    if(Wishlisted){
      var response;
      try {
        response = await _dio.delete(
          'https://project-yhx7.onrender.com/api/v1/wishlist/${widget.product.id.toString()}', // Replace with your API URL
          data: {

          },
          options: Options(
            headers: {
              'Authorization': 'Bearer ${box.read('token')}',
              'Content-Type': 'application/json', // optional, depends on API
            },
          ),
        );

        if (response.statusCode == 200 ) {
          Fluttertoast.showToast(msg:  'Removed Successful');
          setState(() {
            Wishlisted=false;
            favcolor=Colors.white;
          });


        } else {
          Fluttertoast.showToast(msg: response.data?? 'Error in remove from whitelist');
        }
      } catch (e) {
        Fluttertoast.showToast(msg: 'Error: ${e.toString()}');
      }

    }else{
      var response;
      try {
        response = await _dio.post(
          'https://project-yhx7.onrender.com/api/v1/wishlist/${widget.product.id.toString()}', // Replace with your API URL
          data: {

          },
          options: Options(
            headers: {
              'Authorization': 'Bearer ${box.read('token')}',
              'Content-Type': 'application/json', // optional, depends on API
            },
          ),
        );

        if (response.statusCode == 200 ) {
          Fluttertoast.showToast(msg:  'Added Successful');
          setState(() {
            isfav=true;
            favcolor=Colors.red;
          });


        } else {
          Fluttertoast.showToast(msg: response.data?? 'Error in add to whitelist');
        }
      } catch (e) {
        Fluttertoast.showToast(msg: 'Error: ${e.toString()}');
      }
    }




  }
  Future<void> addtoCart() async {

    var response;
    try {
      response = await _dio.post(
        'https://project-yhx7.onrender.com/api/v1/cart/add', // Replace with your API URL
        data: {
          "productId": widget.product.id,
        "size":  widget.product.sizes.length>0?SelectedSize:"no size",
          "quantity":quantity
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer ${box.read('token')}',
            'Content-Type': 'application/json', // optional, depends on API
          },
        ),
      );

      if (response.statusCode == 200 ) {
        setState(() {
          if(productquantity>0){
            productquantity--;
            quantity=1;
          }else{

          }

        });
        Fluttertoast.showToast(msg:  'Added Successful');



      } else {
        Fluttertoast.showToast(msg: response.data?? 'Error Adding to Cart');
      }
    } catch (e) {
      Fluttertoast.showToast(msg: 'Error: ${e.toString()}');
    }






  }
  int _current = 0;
  final CarouselSliderController _controller = CarouselSliderController();

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;


    return Scaffold(
      body:Stack(
        children: [
          SingleChildScrollView(
            child: Column(
              children: [
                SizedBox(
                  height: screenHeight * 0.45,
                  width: double.infinity,
                  child: CarouselSlider(
                    options: CarouselOptions(
                      height: screenHeight * 0.45,
                      viewportFraction: 1.0,
                      autoPlay: true,
                      autoPlayCurve: Curves.easeInExpo,
                      disableCenter: true,
                      onPageChanged: (index, reason) {
                        setState(() {
                          _current = index;
                        });
                      },
                    ),
                    items: widget.product.images.map((item) => Image.network(
                      item,
                      fit: BoxFit.contain,
                      width: double.infinity,
                    )).toList(),
                    carouselController: _controller,
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: 6),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start, // Align children to the start (left)
                    children: [
                      const SizedBox(height: 15),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: widget.product.images.asMap().entries.map((entry) {
                          return GestureDetector(
                            onTap: () => _controller.animateToPage(entry.key),
                            child: Container(
                              width: 12.0,
                              height: 12.0,
                              margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: (Theme.of(context).brightness == Brightness.dark
                                    ? Colors.white
                                    : Colors.black)
                                    .withOpacity(_current == entry.key ? 0.9 : 0.4),
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                      const SizedBox(height: 24),
                      Padding(
                        padding: const EdgeInsets.only(left: 17), // Add left padding like in your JSON
                        child: SizedBox(
                          width: 199,
                          height: 22,
                          child: Text(
                            widget.product.title,
                            style: TextStyle(
                              fontFamily: 'Cairo',
                              fontWeight: FontWeight.bold,
                              fontSize: 24,
                              color: Colors.black,
                              height: 22 / 24,
                            ),
                            textAlign: TextAlign.left,
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),
                      Padding(
                        padding: const EdgeInsets.only(left: 16),
                        child: SizedBox(
                          width: 396,
                          height: 77,
                          child: Text(
                            widget.product.description,
                            style: TextStyle(
                              fontFamily: 'Cairo',
                              fontWeight: FontWeight.normal, // SemiBold equivalent
                              fontSize: 14,
                              color: Colors.black,
                              height: 1.4, // reasonable line height since original is "auto"
                            ),
                            textAlign: TextAlign.left,
                          ),
                        ),
                      ), const SizedBox(height: 24),
                      ProductRatingRow(
                        price:widget.product.price,
                        rating: widget.product.ratingsAverage,
                        reviewCount: widget.product.ratingsQuantity,
                      ),
            
                      Visibility(
                          visible: widget.product.sizes.length>0,
            
                          child: Column(
                            children: [
                              const SizedBox(height: 24),
                              Padding(
                                padding: const EdgeInsets.only(left: 17), // Add left padding like in your JSON
                                child:  Align(
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    'Select Size :',
                                    style: TextStyle(
                                      fontFamily: 'Cairo',
                                      fontWeight: FontWeight.normal,
                                      fontSize: 20,
                                      color: Colors.black,
                                      height: 22 / 24,
                                    ),
                                    textAlign: TextAlign.left,
                                  ),
                                ),
                              ),
                              const SizedBox(height: 24),
                              Padding(
                                padding: const EdgeInsets.only(left: 16),
                                child: Row(
                                  children: widget.product.sizes.map((size) {
                                    return Padding(
                                      padding: const EdgeInsets.only(right: 16), // space between items
                                      child: GestureDetector(
                                        onTap: (){
                                          setState(() {
                                            SelectedSize=size;
                                          });
                                        },
                                        child: Container(
                                          width: 50,
                                          height: 45,
                                          decoration: BoxDecoration(
                                            color: const Color(0xFF263D54), // from your JSON rgba(38, 61, 84)
                                            borderRadius: BorderRadius.circular(10),
                                            border: Border.all(color: Colors.white),
                                          ),
                                          child: Center(
                                            child: Text(
                                              size,
                                              style: const TextStyle(
                                                fontFamily: 'Cairo',
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                                color: Colors.white,

                                              ),

                                            ),
                                          ),
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                ),
                              )
            
            
                            ],
                          )
                      ),
                      const SizedBox(height: 24),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0), // بادينج يمين وشمال
                        child: SizedBox(
                          width: double.infinity,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // مجموعة + 1 -
                              Container(
                                width: 100,
                                height: 40,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(25),
                                  border: Border.all(color: Color(0xFF263D54)),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                  children: [
                                    GestureDetector(
                                      onTap: () {
                                        setState(() {
                                          if(quantity>1){
                                            quantity=quantity-1;
                                            ++productquantity;

                                          }else{
                                            quantity=quantity;
                                          }

                                        });

                                      },
                                      child: Icon(Icons.remove, color: Color(0xFF263D54), size: 20),
                                    ),
                                    Container(
                                      width: 28,
                                      height: 28,
                                      decoration: BoxDecoration(
                                        color: Color(0xFF263D54),
                                        shape: BoxShape.circle,
                                      ),
                                      child: Center(
                                        child: Text(
                                          quantity.toString(),
                                          style: TextStyle(
                                            fontFamily: 'Cairo',
                                            fontSize: 14,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.white,
                                          ),
                                        ),
                                      ),
                                    ),
                                    GestureDetector(
                                      onTap: () {

                                        setState(() {
                                          if(productquantity>0){
                                            quantity=quantity+1;
                                            productquantity=productquantity-1;
                                          }else{
                                            quantity=quantity;
                                          }

                                        });
                                      },
                                      child: Icon(Icons.add, color: Color(0xFF263D54), size: 20),
                                    ),
                                  ],
                                ),
                              ),
            
                              // زر ADD TO CART
                              Expanded(
                                child: GestureDetector(
                                  onTap: (){
                                    if(quantity>0){
                                      addtoCart();
                                    }else{
                                      Fluttertoast.showToast(msg:  'No items in the Stock');

                                    }

                                  },
                                  child: Container(
                                    margin: const EdgeInsets.symmetric(horizontal: 10),
                                    height: 40,
                                    decoration: BoxDecoration(
                                      color: Color(0xFF263D54),
                                      borderRadius: BorderRadius.circular(30),
                                    ),
                                    child: Center(
                                      child: Text(
                                        'ADD TO CART',
                                        style: TextStyle(
                                          fontFamily: 'Cairo',
                                          fontSize: 16,
                                          fontWeight: FontWeight.w600,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
            
                              // أيقونة القلب
                              GestureDetector(
                                onTap: () {
                                  print('pp');
                                  addtofav();
                                },
                                child: Container(
                                  width: 40,
                                  height: 40,
                                  decoration: BoxDecoration(
                                    color: favcolor,
                                    shape: BoxShape.circle,
                                    border: Border.all(color: Color(0xFF263D54)),
                                  ),
                                  child: Icon(Icons.favorite_border, color: Color(0xFF263D54), size: 20),
                                ),
                              ),

                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),
            
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0), // small horizontal padding
                        child: Container(
                          height: 1, // thin line
                          color: Colors.grey.shade400, // or any color you prefer
                        ),
                      ),
                      const SizedBox(height: 24),
                      Padding(
                        padding: const EdgeInsets.only(left: 17), // Add left padding like in your JSON
                        child:  Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            '100 % Original product .',
                            style: TextStyle(
                              fontFamily: 'Cairo',
                              fontWeight: FontWeight.normal,
                              fontSize: 14,
                              color: Colors.black,
                              height: 22 / 24,
                            ),
                            textAlign: TextAlign.left,
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),
                      Padding(
                        padding: const EdgeInsets.only(left: 17), // Add left padding like in your JSON
                        child:  Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            'Cash on delivery is available on this product . ',
                            style: TextStyle(
                              fontFamily: 'Cairo',
                              fontWeight: FontWeight.normal,
                              fontSize: 14,
                              color: Colors.black,
                              height: 22 / 24,
                            ),
                            textAlign: TextAlign.left,
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),
                      Padding(
                        padding: const EdgeInsets.only(left: 17), // Add left padding like in your JSON
                        child:  Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            'Easy return and exchange within 7 days . ',
                            style: TextStyle(
                              fontFamily: 'Cairo',
                              fontWeight: FontWeight.normal,
                              fontSize: 14,
                              color: Colors.black,
                              height: 22 / 24,
                            ),
                            textAlign: TextAlign.left,
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          _buildInfoBox(Icons.clean_hands, "Top Quality", "Best value for your money"),
                          const SizedBox(width: 15), // small padding between boxes
                          _buildInfoBox(Icons.access_time, "Support 24/7", "Contact us 24 hours a day"),
                        ],
                      ),
                      const SizedBox(height: 24),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          _buildInfoBox(Icons.local_shipping, "International\nShipping", "Fast Shipping all over World"),
                          const SizedBox(width: 15), // small padding between boxes
                          _buildInfoBox(Icons.security_outlined, "100% Secure\nPayment", "Your payments are safe with usContact us 24 hours a day"),
                        ],
                      ),
                      const SizedBox(height: 24),


                    ],
                  ),
                ),
            
              ],
            ),
          ),
          // 🔵 السلايدر ياخد ربع الشاشة فقط


          // 🟡 الهيدر فوق الصور
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: CustomHeader2(),
          ),

          // 🟢 محتوى باقي الشاشة يبدأ بعد الصور

        ],
      ),
    );
  }


}
Widget _buildInfoBox(IconData icon, String title, String subtitle) {
  return Container(
    width: 170,
    height: 140,
    padding: const EdgeInsets.all(12),
    decoration: BoxDecoration(
      color: const Color(0xFF263D54),
      borderRadius: BorderRadius.circular(10),
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            Icon(icon, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 19,
                color: Colors.white,
                fontFamily: 'Cairo',
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        Padding(
          padding: subtitle=="Fast Shipping all over World"?EdgeInsets.only(left: 15):subtitle=="Your payments are safe with usContact us 24 hours a day"?EdgeInsets.only(left: 15):EdgeInsets.all(15),
          child: Text(
            subtitle,
            style:  TextStyle(
              fontSize: subtitle=="Fast Shipping all over World"?16:subtitle=="Your payments are safe with usContact us 24 hours a day"?14:18,
              color: Colors.white,
              fontFamily: 'Cairo',
            ),


          ),
        ),
      ],
    ),
  );
}
