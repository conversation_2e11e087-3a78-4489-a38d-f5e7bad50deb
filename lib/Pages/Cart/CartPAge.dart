import 'dart:async';

import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:fluttertoast/fluttertoast.dart';

import '../../Models/Cart/GetCart.dart';
import '../../Models/Cart/Model.dart';
import '../Products/ProductsPage.dart';
import '../Widgets/Billing Dialog.dart';
import '../Widgets/CustomDrawer.dart';
import '../Widgets/Header.dart';

class CartPage extends StatefulWidget {
  const CartPage({Key? key}) : super(key: key);

  @override
  State<CartPage> createState() => _CartPageState();
}

class _CartPageState extends State<CartPage> {
  OrderResponse? order;
  bool showNoData = false;
  void loadData() async {
    setState(() {
      order = null;
    });
    order = await fetchOrder();
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    loadData();
    Timer(const Duration(seconds: 10), () {
      if (mounted && order == null) {
        setState(() {
          showNoData = true;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (order == null) {
      if (showNoData) {
        return const Scaffold(
          body: Center(child: Text('No items on cart')),
        );
      } else {
        return const Scaffold(
          body: Center(child: CircularProgressIndicator()),
        );
      }
    }

    return Scaffold(
      drawer: const CustomDrawer(),
      body: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 160),
            child: SingleChildScrollView(
              child: Column(
                children: [
                  ListView.builder(
                    itemCount: order?.data?.products?.length ?? 0,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (context, index) {
                      final item = order!.data!.products![index];
                      return ProductCard(
                        title: item.product!.name!,
                        subtitle: item.product!.name!,
                        imageUrl: item.product!.imageCover!,
                        price: item.product!.price!.toString(),
                        quantity: item.quantity!,
                        id:item.product!.id!,
                        size: item.size!,
                        onUpdated: loadData,
                      );
                    },
                  ),
                  const SizedBox(height: 20),
                   Visibility(visible:order!=null ,

                     child:                    OrderSummaryCard(total:order!.data!.totalPrice! ,totalafterdiscount:order!.data!.totalPriceAfterDiscount! ,onUpdated: loadData,Orderid: order!.data!.id!,),
                   ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
          const Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: CustomHeader(title: 'Shopping Cart'),
          ),
        ],
      ),
    );
  }
}

class ProductCard extends StatefulWidget {
  final String title;
  final String subtitle;
  final String imageUrl;
  final String price;
   final int quantity;
  final String id;
  final String size;
  final VoidCallback onUpdated;


  const ProductCard({
    super.key,
    required this.title,
    required this.subtitle,
    required this.imageUrl,
    required this.price,
    required this.quantity,
    required this.onUpdated,
    required this.id,
    required this.size
  });

  @override
  State<ProductCard> createState() => _ProductCardState();
}

class _ProductCardState extends State<ProductCard> {
//update qty
  final _dio=Dio();


  void updateqty(qty)async{
    var response;

    try {
      response = await _dio.put(
        'https://project-yhx7.onrender.com/api/v1/cart/update', // Replace with your API URL
        data: {
          "productId":widget.id,
          "quantity": qty,
          "size":widget.size
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer ${box.read('token')}',
            'Content-Type': 'application/json', // optional, depends on API
          },
        ),
      );

      if (response.statusCode == 200 ) {
        Fluttertoast.showToast(msg:  'Updated Successful');
     widget.onUpdated();


      } else {
        Fluttertoast.showToast(msg: response.data?? 'Error in Update');
      }
    } catch (e) {
      Fluttertoast.showToast(msg: 'Error: no qty ');
    }
  }
  void RemoveFromCart()async{
    var response;
    try {
      response = await _dio.delete(
        'https://project-yhx7.onrender.com/api/v1/cart/remove', // Replace with your API URL
        data: {
          "productId":widget.id,
          "size":widget.size

        },
        options: Options(
          headers: {
            'Authorization': 'Bearer ${box.read('token')}',
            'Content-Type': 'application/json', // optional, depends on API
          },
        ),
      );

      if (response.statusCode == 200 ) {
        Fluttertoast.showToast(msg:  'Removed Successful');
        widget.onUpdated();


      } else {
        Fluttertoast.showToast(msg: response.data?? 'Error in Remove');
      }
    } catch (e) {
      Fluttertoast.showToast(msg: 'Error: ${e.toString()}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 2),
      height: 180,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Color(0xFF0988FF)),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Row(
        children: [
          Container(
            width: 130,
            height: 150,
            margin: const EdgeInsets.only(left: 13),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              image: DecorationImage(
                image: NetworkImage(widget.imageUrl),
                fit: BoxFit.cover,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 14),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          widget.title,
                          style: const TextStyle(
                            fontFamily: 'Cairo',
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close, size: 20, color: Colors.grey),
                        onPressed: () {
                          RemoveFromCart();
                        },
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    widget.subtitle,
                    style: const TextStyle(
                      fontFamily: 'Cairo',
                      fontSize: 16,
                    ),
                  ),
                  const Spacer(),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '${widget.price} EGP',
                        style: const TextStyle(
                          fontFamily: 'Cairo',
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF0988FF),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(right: 10),
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                          decoration: BoxDecoration(
                            color: Color(0xFF0988FF),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              GestureDetector(


                                 child: Text(
                                  '-',
                                  style: TextStyle(color: Colors.white, fontSize: 18),
                                                               ),
                                onTap: (){
                                   if(widget.quantity>1){
                                     int qty=widget.quantity;
                                     qty--;
                                     updateqty(qty);
                                   }
                                },
                               ),
                              const SizedBox(width: 8),
                              Text(
                                widget.quantity.toString(),
                                style: const TextStyle(color: Colors.white, fontSize: 16),
                              ),
                              const SizedBox(width: 8),
                               GestureDetector(
                                 onTap: (){
                                   int qty=widget.quantity;
                                   qty++;
                                   updateqty(qty);
                                 },
                                 child: Text(
                                  '+',
                                  style: TextStyle(color: Colors.white, fontSize: 18),
                                                               ),
                               ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  )
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class OrderSummaryCard extends StatefulWidget {
  final double total;
  final double totalafterdiscount;
  final String Orderid;
  final VoidCallback onUpdated;
  const OrderSummaryCard({super.key,required this.total ,required this.totalafterdiscount,required this.onUpdated,required this.Orderid});

  @override
  State<OrderSummaryCard> createState() => _OrderSummaryCardState();
}

class _OrderSummaryCardState extends State<OrderSummaryCard> {
  final _dio=Dio();
  void ApplyCoupon()async{
    var response;

    try {
      response = await _dio.post(
        'https://project-yhx7.onrender.com/api/v1/cart/apply-coupon', // Replace with your API URL
        data: {
          "coupon":couponController.text.toString()
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer ${box.read('token')}',
            'Content-Type': 'application/json', // optional, depends on API
          },
        ),
      );

      if (response.statusCode == 200 ) {
        Fluttertoast.showToast(msg:  'Applied Successful');
        widget.onUpdated();


      } else {
        Fluttertoast.showToast(msg: response.data?? 'Not Correct');
      }
    } catch (e) {
      Fluttertoast.showToast(msg: 'Error: not Correct ');
    }
  }
  final TextEditingController couponController = TextEditingController();

  @override
  void dispose() {
    couponController.dispose();
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {

    double subtotal = this.widget.total;
    double subtotaldis=widget.totalafterdiscount;
    double shippingFee = 30;
    double vat = subtotaldis * 0.14;
    double total = subtotaldis + shippingFee + vat;
    String OID=widget.Orderid;

    return Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        padding: const EdgeInsets.all(18),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: Color(0xFF0988FF)),
          borderRadius: BorderRadius.circular(15),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Order Summary',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                fontFamily: 'Cairo',
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              'Have A Coupon ?',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                fontFamily: 'Cairo',
              ),
            ),
            const SizedBox(height: 12),

            // Coupon Row
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: TextField(
                    controller: couponController, // <-- Make sure you define this
                    decoration: InputDecoration(
                      hintText: 'Enter your discount code',
                      hintStyle: const TextStyle(
                        fontSize: 16,
                        fontFamily: 'Cairo',
                        color: Colors.black54,
                      ),
                      filled: true,
                      fillColor: const Color(0xFFF0F8FF),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(5),
                        borderSide: const BorderSide(color: Color(0xFF0988FF)),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(5),
                        borderSide: const BorderSide(color: Color(0xFF0988FF), width: 2),
                      ),
                    ),
                  ),
                ),

                const SizedBox(width: 10),
                Expanded(
                  flex: 1,
                  child: GestureDetector(
                    onTap: (){
                    ApplyCoupon();
                    },
                    child: Container(
                      height: 50,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: Color(0xFF0988FF),
                        borderRadius: BorderRadius.circular(5),
                        border: Border.all(color: Color(0xFF0988FF)),
                      ),
                      child: const Text(
                        'Apply',
                        style: TextStyle(
                          color: Colors.white,
                          fontFamily: 'Cairo',
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Subtotal
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Subtotal (16 items)',
                    style: TextStyle(fontSize: 16, fontFamily: 'Cairo')),
                Text('${subtotal.toStringAsFixed(0)} EGP',
                    style: const TextStyle(fontSize: 16, fontFamily: 'Cairo')),
              ],
            ),
            const SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Subtotal After Discount',
                    style: TextStyle(fontSize: 16, fontFamily: 'Cairo')),
                Text('${subtotaldis.toStringAsFixed(0)} EGP',
                    style: const TextStyle(fontSize: 16, fontFamily: 'Cairo')),
              ],
            ),
            const SizedBox(height: 10),

            // Shipping
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Shipping Fees',
                    style: TextStyle(fontSize: 16, fontFamily: 'Cairo')),
                Text('${shippingFee.toStringAsFixed(0)} EGP',
                    style: const TextStyle(fontSize: 16, fontFamily: 'Cairo')),
              ],
            ),
            const SizedBox(height: 10),

            // VAT
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('VAT (14%)',
                    style: TextStyle(fontSize: 16, fontFamily: 'Cairo')),
                Text('${vat.toStringAsFixed(0)} EGP',
                    style: const TextStyle(fontSize: 16, fontFamily: 'Cairo')),
              ],
            ),
            const SizedBox(height: 10),
            const Divider(color: Color(0xFF0988FF), thickness: 1),
            const SizedBox(height: 10),

            // Total
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Total',
                    style: TextStyle(
                        fontSize: 16,
                        fontFamily: 'Cairo',
                        fontWeight: FontWeight.bold)),
                Text('${total.toStringAsFixed(0)} EGP',
                    style: const TextStyle(
                        fontSize: 16,
                        fontFamily: 'Cairo',
                        fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: 24),

            // Checkout Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF0988FF),
                  padding: const EdgeInsets.symmetric(vertical: 14),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                onPressed: () {
                  showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (_) =>  BillingAddressDialog(OrderID: OID,),
                  );
                },
                child: const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Checkout',
                      style: TextStyle(
                        fontSize: 16,
                        fontFamily: 'Cairo',
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(width: 6),
                    Icon(Icons.arrow_forward_ios, size: 16, color: Colors.white),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 12),

            // Continue Shopping Button
            SizedBox(
              width: double.infinity,
              child: OutlinedButton(
                style: OutlinedButton.styleFrom(
                  side: const BorderSide(color: Color(0xFF0988FF)),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 14),
                ),
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(builder: (context) => ProductPage()),
                  );
                },
                child: const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.arrow_back_ios, size: 16, color: Color(0xFF0988FF)),
                    SizedBox(width: 6),
                    Text(
                      'Continue Shopping',
                      style: TextStyle(
                        fontSize: 16,
                        fontFamily: 'Cairo',
                        color: Color(0xFF0988FF),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );

  }
}


