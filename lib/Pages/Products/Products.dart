import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';

import '../../Models/Products/ProductModel.dart';
import '../../Models/Category/CategoryModel.dart' as c;
import '../Widgets/ProductsWidget.dart';


class ProductWidget extends StatefulWidget {

 final List<Product> products;
  final List<c.Category> category;
  const ProductWidget ({super.key,required this.products,required this.category});

  @override
  State<ProductWidget > createState() => _ProductWidgetState();
}



class _ProductWidgetState extends State<ProductWidget > {

@override
  void initState() {
    // TODO: implement initState
    super.initState();

  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title box


        const SizedBox(height: 24),

        // Welcome Text

        // Shop Now Button





        Padding(padding: EdgeInsets.fromLTRB(5, 0, 5, 0),child:ProductGrid(products:widget.products,limits: 100,favordelete: true,onRemove: null, ) ,)
        ,
        const SizedBox(height: 24),

      ],
    );
  }
}




