import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import '../../Models/Category/CategoryModel.dart';
import '../../Models/Category/GetCategory.dart';
import '../../Models/Products/GetProducts.dart';
import '../../Models/Products/ProductModel.dart';
import '../Widgets/CustomDrawer.dart';
import '../Widgets/Header.dart';

import '../Widgets/Updates.dart';
import '../Widgets/footer.dart';
import 'Products.dart';


class ProductPage extends StatefulWidget {
  const ProductPage({super.key});

  @override
  State<ProductPage> createState() => _ProductPageState();
}


class _ProductPageState extends State<ProductPage> {
  var widthsort=150;
  var showsort =false;
  var showfilter=false;
  String selectedlabel='Relevant';
  void onOptionSelected (value) {
  if (value == 'High to low') {
    loadDataHigh();
  // Sort high to low
  } else if (value == 'Low to high') {
    loadDataLow();
  // Sort low to high
  } else {
    loadData();
  // Default sort (Relevant)
  }
  setState(() {
    showsort=false;
    showfilter=false;
  });
  }
  final items = [
    'Relevant',
    'High to low',
    'Low to high',
  ];

  List<Product>? products;
  List<Category>? category;
  Set<String> selectedItems = {};
  void onApply (selectedIds) {
  // Do something with selectedIds
  var products2=products;
  products=null;
  setState(() {

  });
  products=products2!.where((element) => selectedItems.toList().contains(element.categoryName)).toList();
  setState(() {

  });
  print("Selected IDs: $selectedIds");
  }
  void loadData() async {
    setState(() {
      products=null;
    });
    products = await fetchProducts();
    print("Loaded ${products!.length} products");
    for (var p in products!) {
      print(p.title);
    }
    setState(() {
      selectedlabel='Relevant';
      widthsort=150;
    });  // Tell Flutter to rebuild UI after loading data
  }
  void loadDataHigh() async {
    setState(() {
      products=null;
    });
    products = await fetchProductsHigh();
    print("Loaded ${products!.length} products");
    for (var p in products!) {
      print(p.title);
    }
    setState(() {
      selectedlabel='High to Low';
      widthsort=130;
    });  // Tell Flutter to rebuild UI after loading data
  }
  void loadDataLow() async {
    setState(() {
      products=null;
    });
    products = await fetchProductsLow();
    print("Loaded ${products!.length} products");
    for (var p in products!) {
      print(p.title);
    }
    setState(() {
      selectedlabel='Low to High';
      widthsort=130;
    });  // Tell Flutter to rebuild UI after loading data
  }
  void loadCategory() async {
    category = await fetchCategory();
    print("Loaded ${category!.length} category");
    for (var p in category!) {
      print(p.name);
    }
    setState(() {});  // Tell Flutter to rebuild UI after loading data
  }

  @override
  void initState() {
    super.initState();
    loadData();
    loadCategory();
  }

  @override
  Widget build(BuildContext context) {
    if (products == null||category==null) {
      // Show loading spinner while data is loading
      return Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      drawer: const CustomDrawer(),
      body: Stack(
        children: [
          /// Scrollable Product List with padding to avoid overlap
          Padding(
            padding: const EdgeInsets.only(top: 180, bottom: 0), // adjust as needed
            child: SingleChildScrollView(
              child: ProductWidget(

                products: products!,
                category: category!,
              ),
            ),
          ),

          /// Sticky Header
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Column(
              children: [
                CustomHeader(title: 'products',),
                Padding(
                  padding: const EdgeInsets.only(top:30,left: 10),
                  child: Column(
                    children: [
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          GestureDetector(
                            onTap: ()=>{
                            setState(() {
                              showfilter=!showfilter;
                              showfilter?showsort=false:showsort=showsort;
                            })




                            },
                            child: Text(
                              'Filters',
                              style: const TextStyle(
                                fontFamily: 'Cairo', // Use your local font name
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Transform.rotate(
                            angle: 90 * 3.1415926535 / 180,
                            child: const Icon(
                              Icons.arrow_forward_ios,
                              size: 12,
                              color: Colors.black,
                            ),
                          ),
                           SizedBox(width: widthsort.toDouble()),
                          GestureDetector(
                            onTap: ()=>{
                              setState(() {
                            showsort=!showsort;
                          showsort? showfilter=false:showfilter=showfilter;
                              })
                            },
                            child: Column(
                              children: [
                                Container(
                                  padding: const EdgeInsets.fromLTRB(15, 5, 16, 5),
                                  decoration: BoxDecoration(
                                    gradient: const LinearGradient(
                                      begin: Alignment.topCenter,
                                      end: Alignment.bottomCenter,
                                      colors: [
                                        Color(0xFFB5D2DE), // #B5D2DE
                                        Color(0xFF76D4DD), // #76D4DD
                                      ],
                                    ),
                                    borderRadius: const BorderRadius.only(
                                      topLeft: Radius.circular(15),
                                      topRight: Radius.circular(15),
                                    ),
                                    border: Border.all(color: Colors.white),
                                  ),
                                  child:  Text(
                                    "Sort By : $selectedlabel",
                                    style: TextStyle(
                                      color: Colors.black,
                                      fontFamily: 'Cairo',
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),

                              ],
                            ),
                          ),

                        ],
                      ),
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Visibility(
                            visible: showfilter,
                            child: Container(
                              width: 160,
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.white),
                                borderRadius: BorderRadius.circular(12), // Rounded corners
                                gradient: const LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    Color.fromRGBO(171, 210, 222, 1),
                                    Color.fromRGBO(124, 212, 221, 1),
                                  ],
                                ),
                              ),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  // Title
                                  Padding(
                                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                                    child: Text(
                                      'Category',
                                      style: const TextStyle(
                                        fontSize: 18,
                                        fontFamily: 'Cairo',
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),

                                  // Scrollable checkbox list
                                  Container(
                                    height: 168, // roughly 3 items tall
                                    child: SingleChildScrollView(
                                      child: Column(
                                        children: category!.map((item) {
                                          return CheckboxListTile(
                                            controlAffinity: ListTileControlAffinity.leading,
                                            contentPadding: const EdgeInsets.symmetric(horizontal: 8),
                                            value: selectedItems.contains(item.id),
                                            shape: RoundedRectangleBorder( // Rounded corners for the checkbox itself
                                              borderRadius: BorderRadius.circular(6),
                                            ),
                                            side: const BorderSide(
                                              color: Colors.black54,
                                              width: 1.5,
                                            ),
                                            tileColor: Colors.white.withOpacity(0.3), // Optional: background color for tile
                                            title: Text(
                                              item.name,
                                              style: const TextStyle(
                                                fontSize: 16,
                                                fontFamily: 'Cairo',
                                                fontWeight: FontWeight.w600,
                                                color: Colors.black,
                                              ),
                                            ),
                                            onChanged: (bool? value) {
                                              setState(() {
                                                if (value == true) {
                                                  selectedItems.add(item.id);
                                                } else {
                                                  selectedItems.remove(item.id);
                                                }
                                              });
                                            },
                                          );

                                        }).toList(),
                                      ),
                                    ),
                                  ),

                                  // Apply Button
                                  Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: ElevatedButton(
                                      onPressed: () {
                                        onApply(selectedItems.toList());
                                      },
                                      child: const Text('Apply'),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),


                          SizedBox(width: 233,),

                             Visibility(
                              visible: showsort,
                              child: Container(
                                width: 142,
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.white),
                                  gradient: const LinearGradient(
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                    colors: [
                                      Color.fromRGBO(171, 210, 222, 1),
                                      Color.fromRGBO(124, 212, 221, 1),
                                    ],
                                  ),
                                ),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: items.map((label) {
                                    return GestureDetector(
                                      onTap: () => onOptionSelected(label),
                                      child: Container(
                                        height: 36,
                                        width: double.infinity,
                                        alignment: Alignment.center,
                                        decoration: BoxDecoration(
                                          border: Border(
                                            bottom: BorderSide(color: Colors.white),
                                          ),
                                        ),
                                        child: Text(
                                          label,
                                          style: const TextStyle(
                                            fontSize: 16,
                                            fontFamily: 'Cairo',
                                            fontWeight: FontWeight.w600,
                                            color: Colors.black,
                                          ),
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                ),
                              ),
                            ),



                        ],


                      ),



                    ],
                  ),

                ),

              ],
            ),
          ),

          /// Sticky Footer
          // Positioned(
          //   bottom: 0,
          //   left: 0,
          //   right: 0,
          //   child: Column(
          //     children: [
          //       UpdatesBanner(),
          //       AppFooter(),
          //     ],
          //   ),
          // ),
        ],
      ),
    );

  }
}

