import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get_storage/get_storage.dart';

import '../../Models/profile/GetProfile.dart';
import '../../Models/profile/ProfileModel.dart';
import '../Acconts/LoginPage.dart';
import '../Favourits/FavoritPage.dart';
import '../Products/ProductsPage.dart';
import '../Widgets/CustomDrawer.dart';
import '../Widgets/Header.dart';
class ProfilePage extends StatefulWidget {
  const ProfilePage({Key? key}) : super(key: key);

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  late final UserModel user;
  final box=GetStorage();
  void loadData() async {

    user = (await fetchUser())!;

    setState(() {
      fullname.text=user.name.toString();
      email.text=user.email.toString();
      img=user.image.toString();
    });  // Tell Flutter to rebuild UI after loading data
  }
  @override
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    loadData();
  }



  DateTime? selectedDate;
  final TextEditingController dateController = TextEditingController();
  final TextEditingController fullname=TextEditingController();
  final TextEditingController email=TextEditingController();
  late String img='';
  final _dio=Dio();
  void updateprofile()async{
    var response;


    try {
      response = await _dio.put(
        'https://project-yhx7.onrender.com/api/v1/users/updateMe', // Replace with your API URL
        data: {
"name":fullname.text.toString()
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer ${box.read('token')}',
            'Content-Type': 'application/json', // optional, depends on API
          },
        ),
      );

      if (response.statusCode == 200 ) {
        Fluttertoast.showToast(msg:  'updated Successful');
        setState(() {
          loadData();
        });



      } else {
        Fluttertoast.showToast(msg: response.data?? 'Error update');
        setState(() {
          img=user.image.toString();
        });
      }
    } catch (e) {
      Fluttertoast.showToast(msg: 'Error: ${e.toString()}');
      setState(() {
        img=user.image.toString();
      });
    }
  }
  @override
  Widget build(BuildContext context) {
    if(img==''){
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }
    return Scaffold(
      drawer: const CustomDrawer(),
      body: Stack(
        children: [
          Padding(padding: const EdgeInsets.only(top: 160),
      child: SingleChildScrollView(
        child:
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children:  [
              Padding(
                padding: const EdgeInsets.only(left:16),
                child: Text(
                  'Profile',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              SizedBox(height: 4),
              Padding(
                padding: const EdgeInsets.only(left: 16),
                child: Text(
                  'View & Update Your Personal and Contact Information',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                ),
              ),
              SizedBox(height: 4),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Container(
                  width: double.infinity,
                  height: 500,
                  padding: const EdgeInsets.all(15),
                  decoration: BoxDecoration(
                    color: const Color(0xFF263D54),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Column(

                    children: [
                      // Profile image with edit icon
                      Stack(
                        alignment: Alignment.bottomRight,
                        children: [
                           CircleAvatar(
                            radius: 50,
                            backgroundImage: NetworkImage(img),
                            backgroundColor: Colors.white,
                          ),
                          InkWell(
                            onTap: () {
                              // TODO: Open image picker
                            },
                            child: CircleAvatar(
                              radius: 16,
                              backgroundColor: Colors.white,
                              child: const Icon(
                                Icons.camera_alt,
                                size: 18,
                                color: Color(0xFF263D54),
                              ),
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Name text
                      Text(
                        'Ahlan ${fullname.text} !',
                        style: const TextStyle(
                          fontFamily: 'Cairo',
                          fontSize: 28,
                          fontWeight: FontWeight.w600, // SemiBold
                          color: Colors.white,
                        ),
                        textAlign: TextAlign.center,
                      ),

                      const SizedBox(height: 8),

                      // Email text
                      Text(
                        email.text,
                        style: const TextStyle(
                          fontFamily: 'Cairo',
                          fontSize: 20,
                          fontWeight: FontWeight.w500, // Medium
                          color: Colors.white,
                        ).copyWith(
                          color: Colors.white.withOpacity(0.8),
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Container(
                        width: 370,
                        height: 45,
                        margin: const EdgeInsets.only(left: 0),
                        child: Row(
                          children: [
                            Container(
                              width: 330,
                              height: 45,
                              margin: const EdgeInsets.only(left: 0),
                              decoration: BoxDecoration(
                                color: const Color(0xFF677787),
                                borderRadius: BorderRadius.circular(15),
                              ),
                              child: Row(
                                children: [
                                  Container(
                                    margin: const EdgeInsets.only(left: 23),
                                    child: Row(
                                      children: [
                                        // Icon
                                        const Icon(
                                          Icons.person, // Replace with your desired icon or SVG
                                          size: 15,
                                          color: Colors.white,
                                        ),
                                        const SizedBox(width: 8),

                                        // Text
                                        const Text(
                                          'Profile',
                                          style: TextStyle(
                                            fontFamily: 'Cairo',
                                            fontSize: 20,
                                            fontWeight: FontWeight.w500,
                                            color: Colors.white,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            )

                          ],
                        ),
                      ),
                      const SizedBox(height: 8),
                      GestureDetector(
                        onTap: (){
                          Navigator.of(context).push(
                            MaterialPageRoute(builder: (context) => FavPage()),
                          );
                        },
                        child: Container(
                          width: 370,
                          height: 45,
                          margin: const EdgeInsets.only(left: 0),
                          child: Row(
                            children: [
                              Container(
                                width: 330,
                                height: 45,
                                margin: const EdgeInsets.only(left: 0),
                                decoration: BoxDecoration(
                                  color: const Color(0xFF677787),
                                  borderRadius: BorderRadius.circular(15),
                                ),
                                child: Row(
                                  children: [
                                    Container(
                                      margin: const EdgeInsets.only(left: 23),
                                      child: Row(
                                        children: [
                                          // Icon
                                          const Icon(
                                            Icons.favorite_outlined, // Replace with your desired icon or SVG
                                            size: 15,
                                            color: Colors.white,
                                          ),
                                          const SizedBox(width: 8),

                                          // Text
                                          const Text(
                                            'Favorite Products',
                                            style: TextStyle(
                                              fontFamily: 'Cairo',
                                              fontSize: 20,
                                              fontWeight: FontWeight.w500,
                                              color: Colors.white,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              )

                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),
                      GestureDetector(
                        onTap: (){
                          Navigator.of(context).push(
                            MaterialPageRoute(builder: (context) => ProductPage()),
                          );
                        },
                        child: Container(
                          width: 370,
                          height: 45,
                          margin: const EdgeInsets.only(left: 0),
                          child: Row(
                            children: [
                              Container(
                                width: 330,
                                height: 45,
                                margin: const EdgeInsets.only(left: 0),
                                decoration: BoxDecoration(
                                  color: const Color(0xFF677787),
                                  borderRadius: BorderRadius.circular(15),
                                ),
                                child: Row(
                                  children: [
                                    Container(
                                      margin: const EdgeInsets.only(left: 23),
                                      child: Row(
                                        children: [
                                          // Icon
                                          const Icon(
                                            Icons.shopping_bag_outlined, // Replace with your desired icon or SVG
                                            size: 15,
                                            color: Colors.white,
                                          ),
                                          const SizedBox(width: 8),

                                          // Text
                                          const Text(
                                            'Products',
                                            style: TextStyle(
                                              fontFamily: 'Cairo',
                                              fontSize: 20,
                                              fontWeight: FontWeight.w500,
                                              color: Colors.white,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              )

                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        width: 370,
                        height: 45,
                        margin: const EdgeInsets.only(left: 0),
                        child: Row(
                          children: [
                            Container(
                              width: 330,
                              height: 45,
                              margin: const EdgeInsets.only(left: 0),
                              decoration: BoxDecoration(
                                color: const Color(0xFF677787),
                                borderRadius: BorderRadius.circular(15),
                              ),
                              child: Row(
                                children: [
                                  Container(
                                    margin: const EdgeInsets.only(left: 23),
                                    child: Row(
                                      children: [
                                        // Icon
                                        const Icon(
                                          Icons.phone, // Replace with your desired icon or SVG
                                          size: 15,
                                          color: Colors.white,
                                        ),
                                        const SizedBox(width: 8),

                                        // Text
                                        const Text(
                                          'Contact US',
                                          style: TextStyle(
                                            fontFamily: 'Cairo',
                                            fontSize: 20,
                                            fontWeight: FontWeight.w500,
                                            color: Colors.white,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            )

                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                      GestureDetector(
                        onTap: (){
                          box.remove('token');
                          box.remove('username');
                          Navigator.of(context).pushAndRemoveUntil(
                            MaterialPageRoute(builder: (context) => LoginPage()),
                                (Route<dynamic> route) => false,
                          );

                        },
                        child: Container(
                          width: 370,
                          height: 45,
                          margin: const EdgeInsets.only(left: 0),
                          child: Row(
                            children: [
                              Container(
                                width: 330,
                                height: 45,
                                margin: const EdgeInsets.only(left: 0),
                                decoration: BoxDecoration(
                                    border: Border.all(
                                      color: Colors.white, // Change to your desired border color
                                      width: 1.5,
                                    ) ,
                                  color: const Color(0xFFA06065),
                                  borderRadius: BorderRadius.circular(15),
                                ),
                                child: Row(
                                  children: [
                                    Container(
                                      margin: const EdgeInsets.only(left: 23),
                                      child: Row(
                                        children: [
                                          // Icon
                                          const Icon(
                                            Icons.logout, // Replace with your desired icon or SVG
                                            size: 15,
                                            color: Colors.white,
                                          ),
                                          const SizedBox(width: 8),

                                          // Text
                                          const Text(
                                            'Sign Out',
                                            style: TextStyle(
                                              fontFamily: 'Cairo',
                                              fontSize: 20,
                                              fontWeight: FontWeight.w500,
                                              color: Colors.white,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              )

                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Padding(
                  padding: const EdgeInsets.all(16),
                  child: Container(
                    width: double.infinity,
                    height: 1210,
                    padding: const EdgeInsets.all(15),
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: Color(0xFF49B2FF), // Change to your desired border color
                        width: 1.5,
                      ),
                      color: const Color(0xFFF4F4F4),
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(left:16),
                          child: Text(
                            'Profile Setting',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        SizedBox(height: 4),
                        Padding(
                          padding: const EdgeInsets.only(left: 16),
                          child: Text(
                            'View & Update Your Personal and Contact Information',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey,
                            ),
                          ),
                        ),
                        SizedBox(height: 8),
                        Padding(
                            padding: const EdgeInsets.all(0),
                            child: Container(

                                height: 300,
                                padding: const EdgeInsets.all(15),
                                decoration: BoxDecoration(

                                  color: const Color(0xFF263D54),
                                  borderRadius: BorderRadius.circular(15),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(10),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      // Title
                                      const Text(
                                        'Contact Information',
                                        style: TextStyle(
                                          fontFamily: 'Cairo',
                                          fontSize: 24,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                        ),
                                      ),
                                      const SizedBox(height: 25),

                                      // ── Email field ─────────────────────────────────────────────
                                      const Text(
                                        'Email Address *',
                                        style: TextStyle(
                                          fontFamily: 'Cairo',
                                          fontSize: 18,
                                          fontWeight: FontWeight.w600,
                                          color: Color(0xFFC0BDBD),
                                        ),
                                      ),
                                      const SizedBox(height: 5),
                                      Container(
                                        height: 52,
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius: BorderRadius.circular(12),
                                          border: Border.all(color: Color(0xFF0D99FF)),
                                        ),
                                        alignment: Alignment.centerLeft,
                                        padding: const EdgeInsets.symmetric(horizontal: 10),
                                        child: TextField(
                                          controller: email,
                                          style: const TextStyle(
                                            fontFamily: 'Cairo',
                                            fontSize: 16,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.black,
                                          ),
                                          decoration: const InputDecoration(
                                            border: InputBorder.none,
                                            isCollapsed: true,
                                          ),
                                        ),
                                      ),

                                      const SizedBox(height: 20),

                                      // ── Phone field ─────────────────────────────────────────────
                                      const Text(
                                        'Phone Number',
                                        style: TextStyle(
                                          fontFamily: 'Cairo',
                                          fontSize: 18,
                                          fontWeight: FontWeight.w600,
                                          color: Color(0xFFC0BDBD),
                                        ),
                                      ),
                                      const SizedBox(height: 5),
                                      Container(
                                        height: 52,
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius: BorderRadius.circular(12),
                                          border: Border.all(color: Color(0xFF0D99FF)),
                                        ),
                                        alignment: Alignment.centerLeft,
                                        padding: const EdgeInsets.symmetric(horizontal: 10),
                                        child: TextField(
                                          controller: TextEditingController(text: '01200320731'),
                                          keyboardType: TextInputType.phone,
                                          style: const TextStyle(
                                            fontFamily: 'Cairo',
                                            fontSize: 16,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.black,
                                          ),
                                          decoration: const InputDecoration(
                                            border: InputBorder.none,
                                            isCollapsed: true,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                )



                            )),
                        SizedBox(height: 8),
                        Padding(
                            padding: const EdgeInsets.all(0),
                            child: Container(

                                height: 700,
                                padding: const EdgeInsets.all(15),
                                decoration: BoxDecoration(

                                  color: const Color(0xFF263D54),
                                  borderRadius: BorderRadius.circular(15),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(10),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      // Title
                                      const Text(
                                        'Personal Information',
                                        style: TextStyle(
                                          fontFamily: 'Cairo',
                                          fontSize: 24,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                        ),
                                      ),
                                      const SizedBox(height: 25),

                                      // ── Email field ─────────────────────────────────────────────
                                      const Text(
                                        'Full Name *',
                                        style: TextStyle(
                                          fontFamily: 'Cairo',
                                          fontSize: 18,
                                          fontWeight: FontWeight.w600,
                                          color: Color(0xFFC0BDBD),
                                        ),
                                      ),
                                      const SizedBox(height: 5),
                                      Container(
                                        height: 52,
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius: BorderRadius.circular(12),
                                          border: Border.all(color: Color(0xFF0D99FF)),
                                        ),
                                        alignment: Alignment.centerLeft,
                                        padding: const EdgeInsets.symmetric(horizontal: 10),
                                        child: TextField(
                                          controller: fullname,
                                          style: const TextStyle(
                                            fontFamily: 'Cairo',
                                            fontSize: 16,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.black,
                                          ),
                                          decoration: const InputDecoration(
                                            border: InputBorder.none,
                                            isCollapsed: true,
                                          ),
                                        ),
                                      ),

                                      const SizedBox(height: 20),

                                      // ── Phone field ─────────────────────────────────────────────
                                      const Text(
                                        'Country',
                                        style: TextStyle(
                                          fontFamily: 'Cairo',
                                          fontSize: 18,
                                          fontWeight: FontWeight.w600,
                                          color: Color(0xFFC0BDBD),
                                        ),
                                      ),
                                      const SizedBox(height: 5),
                                      Container(
                                        height: 52,
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius: BorderRadius.circular(12),
                                          border: Border.all(color: Color(0xFF0D99FF)),
                                        ),
                                        alignment: Alignment.centerLeft,
                                        padding: const EdgeInsets.symmetric(horizontal: 10),
                                        child: TextField(
                                          controller: TextEditingController(text: 'Egypt'),
                                          keyboardType: TextInputType.phone,
                                          style: const TextStyle(
                                            fontFamily: 'Cairo',
                                            fontSize: 16,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.black,
                                          ),
                                          decoration: const InputDecoration(
                                            border: InputBorder.none,
                                            isCollapsed: true,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(height: 20),
                                      const Text(
                                        'City',
                                        style: TextStyle(
                                          fontFamily: 'Cairo',
                                          fontSize: 18,
                                          fontWeight: FontWeight.w600,
                                          color: Color(0xFFC0BDBD),
                                        ),
                                      ),
                                      const SizedBox(height: 5),
                                      Container(
                                        height: 52,
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius: BorderRadius.circular(12),
                                          border: Border.all(color: Color(0xFF0D99FF)),
                                        ),
                                        alignment: Alignment.centerLeft,
                                        padding: const EdgeInsets.symmetric(horizontal: 10),
                                        child: TextField(
                                          controller: TextEditingController(text: 'Ismaillia'),
                                          keyboardType: TextInputType.phone,
                                          style: const TextStyle(
                                            fontFamily: 'Cairo',
                                            fontSize: 16,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.black,
                                          ),
                                          decoration: const InputDecoration(
                                            border: InputBorder.none,
                                            isCollapsed: true,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(height: 20),
                                      const Text(
                                        'Date of Birth',
                                        style: TextStyle(
                                          fontFamily: 'Cairo',
                                          fontSize: 18,
                                          fontWeight: FontWeight.w600,
                                          color: Color(0xFFC0BDBD),
                                        ),
                                      ),
                                      const SizedBox(height: 5),
                                      Container(
                                        height: 52,
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius: BorderRadius.circular(12),
                                          border: Border.all(color: Color(0xFF0D99FF)),
                                        ),
                                        alignment: Alignment.centerLeft,
                                        padding: const EdgeInsets.symmetric(horizontal: 10),
                                        child: TextField(
                                          controller: dateController,
                                          readOnly: true,
                                          onTap: () async {
                                            final DateTime? pickedDate = await showDatePicker(
                                              context: context,
                                              initialDate: selectedDate ?? DateTime.now(),
                                              firstDate: DateTime(1900),
                                              lastDate: DateTime(2100),
                                            );
                                            if (pickedDate != null) {
                                              selectedDate = pickedDate;
                                              dateController.text =
                                              "${pickedDate.day}/${pickedDate.month}/${pickedDate.year}";
                                            }
                                          },
                                          style: const TextStyle(
                                            fontFamily: 'Cairo',
                                            fontSize: 16,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.black,
                                          ),
                                          decoration: const InputDecoration(
                                            border: InputBorder.none,
                                            isCollapsed: true,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(height: 20),
                                 GenderRadioGroup(),
                                    ],
                                  ),
                                )



                            )),
                        SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsets.all(0),
                    child: GestureDetector(
                      onTap: (){
                        updateprofile();
                      },
                      child: Container(
                        width: double.infinity,

                        height: 65,
                        padding: const EdgeInsets.all(15),
                        decoration: BoxDecoration(

                          color: const Color(0xFF263D54),
                          borderRadius: BorderRadius.circular(15),
                        ),
                        child: Column(
                          children: [
                            Text(
                              'Update Profile',
                              style: TextStyle(
                                fontFamily: 'Cairo',
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                    )



                  )





                      ],
                    ),
                  )),




            ],
          )


      )

          ),
          const  Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: CustomHeader(title: 'Profile'),
          ),
        ],
      ),
    );
  }
}
class GenderRadioGroup extends StatefulWidget {
  const GenderRadioGroup({super.key});

  @override
  State<GenderRadioGroup> createState() => _GenderRadioGroupState();
}

class _GenderRadioGroupState extends State<GenderRadioGroup> {
  String gender = 'Female';

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.centerLeft, // Align to left of container
      child: Row(
        mainAxisSize: MainAxisSize.min, // Prevent full width expansion
        children: [
          // Female Option
          Row(
            children: [
              Radio<String>(
                value: 'Female',
                groupValue: gender,
                activeColor: Colors.white,
                fillColor: MaterialStateProperty.all(Colors.white),
                onChanged: (value) {
                  setState(() {
                    gender = value!;
                  });
                },
              ),
              const Text(
                'Female',
                style: TextStyle(
                  fontFamily: 'Cairo',
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(width: 15),

          // Male Option
          Row(
            children: [
              Radio<String>(
                value: 'Male',
                groupValue: gender,
                activeColor: Colors.white,
                fillColor: MaterialStateProperty.all(Colors.white),
                onChanged: (value) {
                  setState(() {
                    gender = value!;
                  });
                },
              ),
              const Text(
                'Male',
                style: TextStyle(
                  fontFamily: 'Cairo',
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

