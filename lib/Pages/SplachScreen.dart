import 'package:flutter/material.dart';

import 'Acconts/LoginPage.dart';
import 'MainPage/MainPage.dart';
import 'package:get_storage/get_storage.dart';

import 'package:fluid_bottom_nav_bar/fluid_bottom_nav_bar.dart';

import 'MainScaffoldPage.dart';


// تأكد أنك عرّفت الصفحة التالية مثلاً:
 // أو غيّرها حسب اسم ملف الصفحة

class SplachScreen extends StatefulWidget {
  @override
  State<SplachScreen> createState() => _MySplachScreenState();
}

class _MySplachScreenState extends State<SplachScreen> {
  final box = GetStorage();

  @override
  void initState() {
    super.initState();

    Future.delayed(const Duration(seconds: 2), () {
      if (!mounted) return; // ✅ Prevent setState after dispose

      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) =>
          box.hasData('username') ?  MainScaffoldPage() : const LoginPage(),
        ),
      );
    });
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        height: 926,
        color: const Color(0xFFF4F4F4),
        child: Stack(
          children: [
            Positioned(
              left: -100,
              top: -190,
              child: SizedBox(
                width: 536,
                height: 1100,
                child: Image.asset(
                  'assets/photo1.png',
                  fit: BoxFit.cover,
                ),
              ),
            ),
            Positioned(
              left: 135,
              top: 120,
              child: Container(
                width: 139,
                height: 48,
                child: Center(
                  child: Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(text: 'M', style: TextStyle(color: Colors.white)),
                        TextSpan(text: 'E', style: TextStyle(color: Color(0xFFFFA726))),
                        TextSpan(text: 'N', style: TextStyle(color: Colors.white)),
                        TextSpan(text: 'G', style: TextStyle(color: Color(0xFFFFA726))),
                      ],
                      style: TextStyle(
                        fontFamily: 'Major Mono Display',
                        fontSize: 40,
                      ),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
            Positioned(
              left: 50,
              top: 235,
              child: SizedBox(
                width: 309,
                height: 35,
                child: Text(
                  'Premium Shopping Experience',
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontFamily: 'Cairo',
                    fontSize: 24,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
            Positioned(
              left: 40,
              top: 466,
              child: SizedBox(
                width: 306,
                height: 35,
                child: Text(
                  'Getting your products ready ..',
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontFamily: 'Cairo',
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
            Positioned(
              left: 130,
              top: 291,
              child: SizedBox(
                width: 150,
                height: 150,
                child: Image.asset(
                  'assets/animation_deli.gif',
                  fit: BoxFit.cover,
                ),
              ),
            ),
            Positioned(
              left: 30,
              top: 532,
              child: Container(
                width: 350,
                height: 10,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15),
                  image: const DecorationImage(
                    image: AssetImage('assets/progress.gif'),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
            Positioned(
              left: 170,
              top: 582,
              child: SizedBox(
                width: 60,
                height: 15,
                child: Image.asset(
                  'assets/extra_anim.gif',
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
