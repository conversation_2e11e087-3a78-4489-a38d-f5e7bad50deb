import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/painting.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/widgets.dart';
import 'package:dio/dio.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get_storage/get_storage.dart';

import 'package:mengv2/Pages/MainPage/MainPage.dart';
import '../MainScaffoldPage.dart';
import '../Widgets/CreateAccountFooter.dart';
import '../Widgets/FaceBookLogin.dart';
import '../Widgets/GoogleLogin.dart';
import '../Widgets/HeaderStyle2.dart';
import '../Widgets/OrWidget.dart';
import 'ResetPassword.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({Key? key}) : super(key: key);

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final Dio _dio = Dio();

  bool isValidEmail(String email) {
    final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+');
    return emailRegex.hasMatch(email);
  }

  Future<void> _login() async {
    final email = _emailController.text.trim();
    final password = _passwordController.text.trim();

    if (!isValidEmail(email)) {
      Fluttertoast.showToast(msg: 'Please enter a valid email');
      return;
    }

    if (password.isEmpty) {
      Fluttertoast.showToast(msg: 'Password cannot be empty');
      return;
    }
    var response;
    try {
      response = await _dio.post(
        'https://project-yhx7.onrender.com/api/v1/auth/login', // Replace with your API URL
        data: {
          'email': email,
          'password': password,
        },
      );
final box=GetStorage();
      if (response.statusCode == 200 ) {
        await box.write('username', response.data['data']["_id"].toString());
        await box.write('token', response.data['token'].toString());
        await box.write('name', response.data['data']["name"].toString());
        await box.write('email', response.data['data']["email"].toString());
        await box.write('image', response.data['data']["image"].toString());

       Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => MainScaffoldPage()),
        );
      } else {
        Fluttertoast.showToast(msg: response.data?? 'Login failed');
      }
    } catch (e) {
      Fluttertoast.showToast(msg: 'Error: ${e.toString()}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            Positioned.fill(
              child: Padding(
                padding: const EdgeInsets.only(bottom: 350),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      SizedBox(height: 100),
                      Text(
                        "Log in",
                        style: TextStyle(
                          fontSize: 30,
                          fontFamily: 'Cairo',
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                      // SizedBox(height: 20),
                      // Text(
                      //   "Log in with",
                      //   style: TextStyle(
                      //     fontSize: 20,
                      //     fontFamily: 'Cairo',
                      //     color: Colors.black,
                      //   ),
                      // ),
                      // SizedBox(height: 20),
                      // Padding(
                      //   padding: EdgeInsets.only(left: 12, right: 12),
                      //   child: Row(
                      //     children: [
                      //       FacebookSignInButton(),
                      //       SizedBox(width: 20),
                      //       GoogleInButton(),
                      //     ],
                      //   ),
                      // ),
                      // SizedBox(height: 20),
                      // OrDivider(),
                      SizedBox(height: 20),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16),
                        child: Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            "Enter your email and password",
                            style: TextStyle(
                              fontSize: 18,
                              fontFamily: 'Cairo',
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: 20),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16),
                        child: Container(
                          height: 52,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            border: Border.all(color: Color(0xFF263D54)),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16),
                            child: Center(
                              child: TextField(
                                controller: _emailController,
                                style: TextStyle(
                                  fontFamily: 'Cairo',
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black,
                                ),
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  hintText: 'Email',
                                  hintStyle: TextStyle(
                                    fontFamily: 'Cairo',
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.black54,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: 20),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16),
                        child: Container(
                          height: 52,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            border: Border.all(color: Color(0xFF263D54)),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16),
                            child: Center(
                              child: TextField(
                                controller: _passwordController,
                                obscureText: true,
                                style: TextStyle(
                                  fontFamily: 'Cairo',
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black,
                                ),
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  hintText: 'Password',
                                  hintStyle: TextStyle(
                                    fontFamily: 'Cairo',
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.black54,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: 10),
                      Align(
                        alignment: Alignment.centerRight,
                        child: Padding(
                          padding: const EdgeInsets.only(right: 16),
                          child: GestureDetector(
                            onTap: () {
                              Navigator.of(context).push(
                                MaterialPageRoute(builder: (context) => ResetpasswordPage()),
                              );
                              // TODO: handle forgot password
                            },
                            child: Text(
                              'Forget your password ?',
                              style: TextStyle(
                                fontFamily: 'Cairo',
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: Color(0xFFBF4648),
                              ),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: 20),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: SizedBox(
                          width: double.infinity,
                          height: 52,
                          child: ElevatedButton(
                            onPressed: _login,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Color(0xFF263D54),
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                                side: BorderSide(color: Colors.white),
                              ),
                              elevation: 0,
                            ),
                            child: Text(
                              'Log In',
                              style: TextStyle(
                                fontFamily: 'Cairo',
                                fontSize: 20,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ),
            Positioned(top: 0, left: 0, right: 0, child: CustomHeader2()),
            Positioned(bottom: 0, left: 0, right: 0, child: CreateAccountCardWithBackground()),
          ],
        ),
      ),
    );
  }
}
