import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/painting.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/widgets.dart';
import 'package:dio/dio.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get_storage/get_storage.dart';

import 'package:mengv2/Pages/MainPage/MainPage.dart';
import '../MainScaffoldPage.dart';
import '../Widgets/CreateAccountFooter.dart';
import '../Widgets/FaceBookLogin.dart';
import '../Widgets/GoogleLogin.dart';
import '../Widgets/HeaderStyle2.dart';
import '../Widgets/OrWidget.dart';
import 'ResetCode.dart';

class ResetpasswordPage extends StatefulWidget {
  const ResetpasswordPage({Key? key}) : super(key: key);

  @override
  State<ResetpasswordPage> createState() => _ResetpasswordPageState();
}

class _ResetpasswordPageState extends State<ResetpasswordPage> {
  final TextEditingController _emailController = TextEditingController();

  final Dio _dio = Dio();

  bool isValidEmail(String email) {
    final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+');
    return emailRegex.hasMatch(email);
  }

  Future<void> _login() async {
    final email = _emailController.text.trim();


    if (!isValidEmail(email)) {
      Fluttertoast.showToast(msg: 'Please enter a valid email');
      return;
    }


    var response;
    try {
      response = await _dio.post(
        'https://project-yhx7.onrender.com/api/v1/auth/forgotPassword', // Replace with your API URL
        data: {
          'email': email,

        },
      );
      final box=GetStorage();
      box.write('remail', email);

      if (response.statusCode == 200 ) {
        Fluttertoast.showToast(msg:  'We Send You Reset Code');

        Navigator.of(context).push(
          MaterialPageRoute(builder: (context) => ResetcodePage()),
        );
      } else {
        Fluttertoast.showToast(msg:  'failed');
      }
    } catch (e) {
      Fluttertoast.showToast(msg: 'Error: ${e.toString()}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            Positioned.fill(
              child: Padding(
                padding: const EdgeInsets.only(bottom: 350),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      SizedBox(height: 100),
                      Text(
                        "Forgot your password ?",
                        style: TextStyle(
                          fontSize: 30,
                          fontFamily: 'Cairo',
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                      SizedBox(height: 20),
                      Text(
                        "Please enter your email address below",
                        style: TextStyle(
                          fontSize: 20,
                          fontFamily: 'Cairo',
                          color: Colors.black,
                        ),
                      ),
                      SizedBox(height: 20),
                      Text(
                        "to receive a password reset link.",
                        style: TextStyle(
                          fontSize: 20,
                          fontFamily: 'Cairo',
                          color: Colors.black,
                        ),
                      ),
                      SizedBox(height: 20),


                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16),
                        child: Container(
                          height: 52,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            border: Border.all(color: Color(0xFF263D54)),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16),
                            child: Center(
                              child: TextField(
                                controller: _emailController,
                                style: TextStyle(
                                  fontFamily: 'Cairo',
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black,
                                ),
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  hintText: 'Email',
                                  hintStyle: TextStyle(
                                    fontFamily: 'Cairo',
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.black54,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),

                      SizedBox(height: 10),

                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: SizedBox(
                          width: double.infinity,
                          height: 52,
                          child: ElevatedButton(
                            onPressed: _login,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Color(0xFF263D54),
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                                side: BorderSide(color: Colors.white),
                              ),
                              elevation: 0,
                            ),
                            child: Text(
                              'Send',
                              style: TextStyle(
                                fontFamily: 'Cairo',
                                fontSize: 20,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ),
            Positioned(top: 0, left: 0, right: 0, child: CustomHeader2()),
            Positioned(bottom: 0, left: 0, right: 0, child: CreateAccountCardWithBackground()),
          ],
        ),
      ),
    );
  }
}
