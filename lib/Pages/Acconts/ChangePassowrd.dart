import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/painting.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/widgets.dart';
import 'package:dio/dio.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get_storage/get_storage.dart';
import 'package:mengv2/Pages/Acconts/LoginPage.dart';

import 'package:mengv2/Pages/MainPage/MainPage.dart';
import '../MainScaffoldPage.dart';
import '../Widgets/CreateAccountFooter.dart';
import '../Widgets/FaceBookLogin.dart';
import '../Widgets/GoogleLogin.dart';
import '../Widgets/HeaderStyle2.dart';
import '../Widgets/LoginFooter.dart';
import '../Widgets/OrWidget.dart';

class ChangepassowrdPage extends StatefulWidget {
  const ChangepassowrdPage({Key? key}) : super(key: key);

  @override
  State<ChangepassowrdPage> createState() => _ChangepassowrdPageState();
}

class _ChangepassowrdPageState extends State<ChangepassowrdPage> {

  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _ConfirmpasswordController = TextEditingController();




  final Dio _dio = Dio();



  Future<void> _login() async {

    final password = _passwordController.text.trim();
    final cpass=_ConfirmpasswordController.text.trim();



    if (password.isEmpty) {
      Fluttertoast.showToast(msg: 'Password cannot be empty');
      return;
    }
    if (password.toString()!=cpass.toString()) {
      Fluttertoast.showToast(msg: 'Passwords not matches');
      return;
    }

    var response;

    try {
      final box=GetStorage();
      response = await _dio.put(
        'https://project-yhx7.onrender.com/api/v1/auth/resetPassword', // Replace with your API URL
        data: {
          'email': box.read('remail'),
          'password': password,
          'resetCode':box.read('rcode')

        },
      );

      if (response.statusCode == 200 ) {


        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => LoginPage()),
        );
      } else {
        Fluttertoast.showToast(msg: 'change feild');
      }
    } catch (e) {
      print(e.toString());
      Fluttertoast.showToast(msg: 'Error: ${e.toString()}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            Positioned.fill(
              child: Padding(
                padding: const EdgeInsets.only(bottom: 350),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      SizedBox(height: 100),
                      Text(
                        "Enter new Password",
                        style: TextStyle(
                          fontSize: 30,
                          fontFamily: 'Cairo',
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),





                      SizedBox(height: 20),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16),
                        child: Container(
                          height: 52,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            border: Border.all(color: Color(0xFF263D54)),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16),
                            child: Center(
                              child: TextField(
                                controller: _passwordController,
                                obscureText: true,
                                style: TextStyle(
                                  fontFamily: 'Cairo',
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black,
                                ),
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  hintText: 'Password',
                                  hintStyle: TextStyle(
                                    fontFamily: 'Cairo',
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.black54,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: 20),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16),
                        child: Container(
                          height: 52,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            border: Border.all(color: Color(0xFF263D54)),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16),
                            child: Center(
                              child: TextField(
                                controller: _ConfirmpasswordController,
                                obscureText: true,
                                style: TextStyle(
                                  fontFamily: 'Cairo',
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black,
                                ),
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  hintText: 'Confirm Password',
                                  hintStyle: TextStyle(
                                    fontFamily: 'Cairo',
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.black54,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),

                      SizedBox(height: 20),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: SizedBox(
                          width: double.infinity,
                          height: 52,
                          child: ElevatedButton(
                            onPressed: _login,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Color(0xFF263D54),
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                                side: BorderSide(color: Colors.white),
                              ),
                              elevation: 0,
                            ),
                            child: Text(
                              'Done',
                              style: TextStyle(
                                fontFamily: 'Cairo',
                                fontSize: 20,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ),
            Positioned(top: 0, left: 0, right: 0, child: CustomHeader2()),
            Positioned(bottom: 0, left: 0, right: 0, child: CreateAccountCardWithBackground()),
          ],
        ),
      ),
    );
  }
}
