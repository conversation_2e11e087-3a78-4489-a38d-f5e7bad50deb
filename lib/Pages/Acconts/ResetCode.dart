import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/painting.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/widgets.dart';
import 'package:dio/dio.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get_storage/get_storage.dart';
import 'package:mengv2/Pages/Acconts/ChangePassowrd.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

import 'package:mengv2/Pages/MainPage/MainPage.dart';
import '../MainScaffoldPage.dart';
import '../Widgets/CreateAccountFooter.dart';
import '../Widgets/FaceBookLogin.dart';
import '../Widgets/GoogleLogin.dart';
import '../Widgets/HeaderStyle2.dart';
import '../Widgets/OrWidget.dart';

class ResetcodePage extends StatefulWidget {
  const ResetcodePage({Key? key}) : super(key: key);

  @override
  State<ResetcodePage> createState() => _ResetcodePageState();
}

class _ResetcodePageState extends State<ResetcodePage> {
  final TextEditingController _code= TextEditingController();

  final Dio _dio = Dio();


  Future<void> _login() async {
    final code=_code.text.trim();





    var response;
    try {
      response = await _dio.post(
        'https://project-yhx7.onrender.com/api/v1/auth/verifyResetCode', // Replace with your API URL
        data: {
          'resetCode': code,

        },
      );
      final box=GetStorage();
      if (response.statusCode == 200 ) {
        Fluttertoast.showToast(msg: 'You can now change Password');
        box.write('rcode', _code.text.toString());

        Navigator.of(context).push(
          MaterialPageRoute(builder: (context) => ChangepassowrdPage()),
        );
      } else {
        Fluttertoast.showToast(msg:  'failed');
      }
    } catch (e) {
      Fluttertoast.showToast(msg: 'Error: ${e.toString()}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            Positioned.fill(
              child: Padding(
                padding: const EdgeInsets.only(bottom: 350),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      SizedBox(height: 100),
                      Text(
                        "Check in your mail !",
                        style: TextStyle(
                          fontSize: 30,
                          fontFamily: 'Cairo',
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                      SizedBox(height: 20),
                      Text(
                        "We just emailed you with instruction to ",
                        style: TextStyle(
                          fontSize: 20,
                          fontFamily: 'Cairo',
                          color: Colors.black,
                        ),
                      ),
                      SizedBox(height: 20),
                      Text(
                        "reset your password",
                        style: TextStyle(
                          fontSize: 20,
                          fontFamily: 'Cairo',
                          color: Colors.black,
                        ),
                      ),
                      SizedBox(height: 20),


                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16),
                        child: PinCodeTextField(
                          appContext: context,
                          length: 6,
                          controller: _code,
                          obscureText: false,
                          animationType: AnimationType.fade,
                          pinTheme: PinTheme(
                            shape: PinCodeFieldShape.box,
                            borderRadius: BorderRadius.circular(12),
                            fieldHeight: 52,
                            fieldWidth: 45,
                            activeColor: Color(0xFF263D54),
                            inactiveColor: Color(0xFF263D54),
                            selectedColor: Colors.blue,
                          ),
                          textStyle: TextStyle(
                            fontFamily: 'Cairo',
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                          ),
                          animationDuration: Duration(milliseconds: 300),
                          enableActiveFill: false,
                          keyboardType: TextInputType.number,
                          onCompleted: (v) {
                            print("Code entered: $v");
                          },
                          onChanged: (value) {
                            print(value);
                          },
                        ),
                      ),

                      SizedBox(height: 10),

                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: SizedBox(
                          width: double.infinity,
                          height: 52,
                          child: ElevatedButton(
                            onPressed: _login,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Color(0xFF263D54),
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                                side: BorderSide(color: Colors.white),
                              ),
                              elevation: 0,
                            ),
                            child: Text(
                              'Done',
                              style: TextStyle(
                                fontFamily: 'Cairo',
                                fontSize: 20,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ),
            Positioned(top: 0, left: 0, right: 0, child: CustomHeader2()),
            Positioned(bottom: 0, left: 0, right: 0, child: CreateAccountCardWithBackground()),
          ],
        ),
      ),
    );
  }
}
