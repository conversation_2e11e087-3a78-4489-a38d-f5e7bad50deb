import 'package:flutter/material.dart';

import '../../Models/Products/GetProducts.dart';
import '../../Models/Products/ProductModel.dart';
import '../Widgets/CustomDrawer.dart';
import 'products.dart';
import '../Widgets/Header.dart';

class FavPage extends StatefulWidget {
  const FavPage({Key? key}) : super(key: key);

  @override
  State<FavPage> createState() => _FavPageState();
}

class _FavPageState extends State<FavPage> {
  List<Product>? products;
  void loadData() async {
    setState(() {
      products=null;
    });
    products = await fetchProductsfav();
    print("Loaded ${products!.length} products");
    for (var p in products!) {
      print(p.title);
    }
    setState(() {

    });

  }
  void removeproduct(id){
    products?.removeWhere((element) => element.id==id);
    setState(() {

    });
  }
  @override
  void initState() {
    super.initState();
    loadData();

  }
  @override
  Widget build(BuildContext context) {
    if (products == null) {
      // Show loading spinner while data is loading
      return Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }
    return  Scaffold(
      drawer: const CustomDrawer(),
      body: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 180, bottom: 0), // adjust as needed
            child: SingleChildScrollView(
              child: ProductWidget(
                onRemove: removeproduct,

                products: products!,
                category: [],
              ),
            ),
          ),
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Column(
              children: [
                CustomHeader(title: 'Favorite',),


              ],
            ),
          ),
        ],
      ),
    );
  }
}
