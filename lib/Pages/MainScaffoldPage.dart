import 'package:flutter/material.dart';
import 'package:fluid_bottom_nav_bar/fluid_bottom_nav_bar.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mengv2/Pages/MainPage/MainPage.dart';

import 'Cart/CartPAge.dart';
import 'Favourits/FavoritPage.dart';
import 'Profile/Profile.dart';
import 'Widgets/CustomDrawer.dart';
import 'Widgets/navigation_controller.dart';

class MainScaffoldPage extends StatefulWidget {
  MainScaffoldPage({super.key});

  @override
  State<MainScaffoldPage> createState() => _MainScaffoldPageState();
}

class _MainScaffoldPageState extends State<MainScaffoldPage> {
  final _pages = const [
    MainPage(),
    FavPage(),
    CartPage(),
    ProfilePage(),
  ];

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<int>(
      valueListenable: currentIndexNotifier,
      builder: (context, index, _) {
        return Scaffold(
          drawer: const CustomDrawer(),
          body: _pages[index],
          bottomNavigationBar: FluidNavBar(
            defaultIndex: currentIndexNotifier.value,
            onChange: (i) {


              setState(() {
                currentIndexNotifier.value = i;
              });
            },
            style: const FluidNavBarStyle(
              iconUnselectedForegroundColor: Color(0xFFB1ABAB),
              iconSelectedForegroundColor: Colors.white,
              barBackgroundColor: Color(0xFF192A3B),
            ),
            icons:  [
              FluidNavBarIcon(icon: Icons.home),
              FluidNavBarIcon(icon: Icons.favorite),
              FluidNavBarIcon(icon: FontAwesomeIcons.cartShopping),
              FluidNavBarIcon(icon: Icons.person),
            ],
          ),
        );
      },
    );
  }
}
