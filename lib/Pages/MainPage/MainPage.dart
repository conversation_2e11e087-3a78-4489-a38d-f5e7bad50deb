import 'package:flutter/material.dart';
import '../../Models/Category/CategoryModel.dart';
import '../../Models/Category/GetCategory.dart';
import '../../Models/Products/GetProducts.dart';
import '../../Models/Products/ProductModel.dart';
import '../Widgets/CustomDrawer.dart';
import '../Widgets/Header.dart';
import 'Content.dart';
import '../Widgets/Updates.dart';
import '../Widgets/footer.dart';

class MainPage extends StatefulWidget {
  const MainPage({super.key});

  @override
  State<MainPage> createState() => _MainPageState();
}


class _MainPageState extends State<MainPage> {
  List<Product>? products;
  List<Category>? category;

  void loadData() async {
    products = await fetchProducts();
    print("Loaded ${products!.length} products");
    for (var p in products!) {
      print(p.title);
    }
    setState(() {});  // Tell Flutter to rebuild UI after loading data
  }
  void loadCategory() async {
    category = await fetchCategory();
    print("Loaded ${category!.length} products");
    for (var p in category!) {
      print(p.name);
    }
    setState(() {});  // Tell Flutter to rebuild UI after loading data
  }

  @override
  void initState() {
    super.initState();
    loadData();
    loadCategory();
  }

  @override
  Widget build(BuildContext context) {
    if (products == null||category==null) {
      // Show loading spinner while data is loading
      return Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      drawer: const CustomDrawer(),
      body: Stack(
        children: [
          // Scrollable section with padding
          Padding(
            padding: const EdgeInsets.only(top: 120, bottom: 0), // adjust based on your header/footer heights
            child: SingleChildScrollView(
              child: HomePageHeader(
                products: products!,
                category: category!,
              ),
            ),
          ),

          // Sticky Header (CustomHeader + UpdatesBanner)
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Column(
              children: [
                CustomHeader(title: 'Home'),

              ],
            ),
          ),

          // Sticky Footer
          // Positioned(
          //   bottom: 0,
          //   left: 0,
          //   right: 0,
          //   child: Column(
          //     children: [
          //       UpdatesBanner(),
          //       AppFooter(),
          //     ],
          //   ),
          // ),
        ],
      ),
    );

  }
}

