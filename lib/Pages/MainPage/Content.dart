import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/widgets.dart';

import '../../Models/Products/ProductModel.dart';
import '../../Models/Category/CategoryModel.dart' as c;
import '../Products/ProductsPage.dart';
import '../Widgets/ProductsWidget.dart';


class HomePageHeader extends StatefulWidget {

 final List<Product> products;
  final List<c.Category> category;
  const HomePageHeader({super.key,required this.products,required this.category});

  @override
  State<HomePageHeader> createState() => _HomePageHeaderState();
}



class _HomePageHeaderState extends State<HomePageHeader> {
  int _current = 0;
  final CarouselSliderController _controller = CarouselSliderController();
  late final List<Widget>? imageSliders;
@override
  void initState() {
    // TODO: implement initState
    super.initState();
    imageSliders =widget.products.take(4)
  .map((item) => Container(
  child: Container(
  margin: EdgeInsets.all(0),
  child: ClipRRect(
  borderRadius: BorderRadius.all(Radius.circular(15)),
  child: Stack(
  children: <Widget>[
  Image.network(item.imageCover, fit: BoxFit.cover, width: 320,height: 480,),
  Positioned(
  bottom: 0.0,
  left: 0.0,
  right: 0.0,
  child: Container(
  decoration: BoxDecoration(
  gradient: LinearGradient(
  colors: [
  Color.fromARGB(200, 0, 0, 0),
  Color.fromARGB(0, 0, 0, 0)
  ],
  begin: Alignment.bottomCenter,
  end: Alignment.topCenter,
  ),
  ),
  padding: EdgeInsets.symmetric(
  vertical: 10.0, horizontal: 20.0),

  ),
  ),
  ],
  )),
  ),
  ))
  .toList();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title box


        const SizedBox(height: 24),

        // Welcome Text
        const Padding(
          padding: EdgeInsets.only(left: 16),
          child: SizedBox(
            width: 303,
            height: 40,
            child: Text(
              'Welcome to our App .',
              textAlign: TextAlign.left,
              style: TextStyle(
                fontFamily: 'Cairo',
                fontSize: 28,
                fontWeight: FontWeight.w600, // SemiBold
                height: 40 / 28,
                color: Color.fromRGBO(35, 58, 82, 1),
              ),
            ),
          ),
        ),

        const SizedBox(height: 24),

        // Shop Now Button
        GestureDetector(
          onTap: (){
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (context) => ProductPage()),
            );
          },
          child: Container(
            margin: const EdgeInsets.only(left: 16),
            width: 206,
            height: 50,
            decoration: BoxDecoration(
              color: const Color.fromRGBO(38, 61, 84, 1),
              borderRadius: BorderRadius.circular(15),
            ),
            child: Row(
              children: [
                const Padding(
                  padding: EdgeInsets.only(left: 16),
                  child: Text(
                    'Shop Now',
                    style: TextStyle(
                      fontFamily: 'Cairo',
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      height: 40 / 20,
                    ),
                  ),
                ),
                const Spacer(),
                Container(
                  width: 60,
                  height: 36,
                  margin: const EdgeInsets.only(right: 8),
                  child: Image.asset(
                    'assets/arrow.gif',  // Your GIF file here
                    fit: BoxFit.contain,
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 24),
        Container(
          child: CarouselSlider(
            options: CarouselOptions(
              autoPlayCurve: Curves.easeInExpo,
              autoPlay: true,
              // aspectRatio: 2.0,
              enlargeCenterPage: true,
              clipBehavior: Clip.hardEdge,

              onPageChanged: (index, reason) {
    setState(() {
    _current = index;
    });
    },

            ),
            items: imageSliders,
            carouselController: _controller,

          ),

        ),
        const SizedBox(height: 24),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: widget.products.asMap().entries.take(4).map((entry) {
            return GestureDetector(
              onTap: () => _controller.animateToPage(entry.key),
              child: Container(
                width: 12.0,
                height: 12.0,
                margin: EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
                decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: (Theme.of(context).brightness == Brightness.dark
                        ? Colors.white
                        : Colors.black)
                        .withOpacity(_current == entry.key ? 0.9 : 0.4)),
              ),
            );
          }).toList(),
        ),

        const SizedBox(height: 24),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: widget.category.take(4).map((item) {
            return Column(
              children: [
                Container(
                  width: 85,
                  height: 85,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    image: DecorationImage(
                      image: AssetImage('assets/123.png'),
                      fit: BoxFit.cover,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.25),
                        blurRadius: 10,
                        spreadRadius: .5,
                        offset: const Offset(0, 0),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                SizedBox(
                  width: 85,
                  child: Text(
                    item.name,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 12,
                      fontFamily: 'Cairo',
                      height: 22 / 14,
                    ),
                  ),
                ),
              ],
            );
          }).toList(),
        ),
        const SizedBox(height: 24),
    Container(
    margin: const EdgeInsets.only(left: 15,right: 15),
    width: 396,
    height: 160,
    decoration: BoxDecoration(
    borderRadius: BorderRadius.circular(15),
    boxShadow: [
    BoxShadow(
    color: Colors.black.withOpacity(0.25),
    blurRadius: 5,
    spreadRadius: 5,
    offset: Offset(0, 0),
    ),
    ],
    border: Border.all(color: Colors.white, width: 1),
    image: const DecorationImage(
    image: AssetImage(
    'assets/Rectangle 8.png',
    ),
    fit: BoxFit.cover,
    ),
    ),
    child: Container(
    // Overlay gradient to improve text readability
    decoration: BoxDecoration(
    borderRadius: BorderRadius.circular(15),
    gradient: LinearGradient(
    colors: [
    Colors.black.withOpacity(0.6),
    Colors.black.withOpacity(0.1),
    ],
    begin: Alignment.bottomCenter,
    end: Alignment.topCenter,
    ),
    ),
    padding: const EdgeInsets.only(left: 24, top: 26, right: 16),
    child: Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
    Text(
    'New Arrivals',
    style: const TextStyle(
    fontFamily: 'Cairo',
    fontWeight: FontWeight.bold,
    fontSize: 20,
    color: Colors.white,
    ),
    ),
    const SizedBox(height: 13),
    Text(
    'Get ready for our products',
    style: const TextStyle(
    fontFamily: 'Cairo',
    fontWeight: FontWeight.w600, // SemiBold
    fontSize: 16,
    color: Colors.white,
    height: 22 / 16,
    ),
    ),
    const Spacer(),
    GestureDetector(
      onTap: ()=>{
        Navigator.of(context).push(
          MaterialPageRoute(builder: (context) => ProductPage()),
        )
      },
      child: Container(
      width: 120,
      height: 30,
      decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(10),
      border: Border.all(color: const Color(0xFF263D54)),
      ),
      child: Center(
      child: Text(
      'Shop Now',
      style: const TextStyle(
      fontFamily: 'Cairo',
      fontWeight: FontWeight.w500, // Medium
      fontSize: 16,
      color: Color(0xFF263D54),
      ),
      ),
      ),
      ),
    ),
    const SizedBox(height: 10),
    ],
    ),
    ),
    ),
        const SizedBox(height: 24),
        Padding(padding: EdgeInsets.fromLTRB(5, 0, 5, 0),child:ProductGrid(products:widget.products,limits:6 ,favordelete: true,onRemove: null,) ,)
        ,
        const SizedBox(height: 24),

      ],
    );
  }
}




